import { useParams, useNavigate } from "react-router-dom";
import { useEffect } from "react";
import useNewsDetailsStore from "../store/newsDetailsStore"; 
import DOMPurify from "dompurify";
import constructUrl from "../utils/constructUrl";
import { FiArrowLeft } from "react-icons/fi";
import ViewText from "../components/ViewText"; 
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";

export default function ArticleDetails() {
  const { id } = useParams();
  const navigate = useNavigate();

  const {
    article,
    articleDetailsLoading,
    fetchArticleDetails,
    error,
  } = useNewsDetailsStore();

  useEffect(() => {
    fetchArticleDetails(id);
  }, [id, fetchArticleDetails]);

  if (articleDetailsLoading) {
    return (
      <div className="max-w-5xl mx-auto px-4 md:px-8 py-16 space-y-6">
        <Skeleton width={120} height={40} />
        <Skeleton height={48} width="80%" />
        <Skeleton height={32} width="60%" />
        <Skeleton height={400} borderRadius={16} />
        <div className="space-y-4">
          <Skeleton count={6} />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto py-16 text-center text-red-500 text-lg">
        {error}
      </div>
    );
  }

  if (!article || article.length === 0) {
    return (
      <div className="max-w-4xl mx-auto py-16 text-center text-gray-500 text-lg">
        Article not found.
      </div>
    );
  }

  const current = article;
  const formattedDate = new Date(current.createdAt).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  return (
    <article className="  mx-auto px-4 md:px-8 py-16 relative bg-[#f9f9f7] ">
<div className="sticky top-6 z-20">
        <button
          onClick={() => navigate(-1)}
          className="inline-flex items-center gap-2 px-5 py-2 rounded-lg font-semibold 
            bg-[#bea15d] text-white shadow-md transition 
            hover:bg-[#a88d50] focus:ring-2 focus:ring-[#bea15d]/60 focus:outline-none"
        >
          <FiArrowLeft className="text-lg" />
          Back to News
        </button>
      </div>
      <div className="max-w-7xl mx-auto">

   
      {/* Sticky Back button */}
      

      {/* Title */}
      <h1 className="text-5xl font-bold text-gray-900 leading-tight mb-6 tracking-tight mt-10">
        {current.title}
      </h1>

  {/* Meta info */}
<div className="flex flex-wrap items-center gap-4 text-sm md:text-base w-fit text-gray-600 mb-10   ">
  <span className="flex items-center gap-2">
    <span className="font-semibold text-gray-800 text-xl">Published:</span>
    <span className="text-primary font-bold text-xl">{formattedDate}</span>
  </span>
  <span className="flex items-center gap-2">
    <span className="font-semibold text-gray-800 text-xl">Reading Time:</span>
    <span className="text-primary font-bold text-xl">{current.readingTime} min</span>
  </span>
</div>

<hr className="border-gray-300 my-6" />
      {/* Image */}
      {current.img && (
        <div className="mb-12">
          <img
            src={constructUrl(current.img)}
            alt={current.title}
            className="w-full max-h-[600px] object-cover rounded-2xl shadow-lg"
          />
        </div>
      )}

      {/* Content */}
      <div className="prose prose-lg md:prose-xl ql-snow ql-editor prose-gray max-w-none leading-relaxed text-gray-800">
        <ViewText content={DOMPurify.sanitize(current.content)} />
      </div>
         </div>
    </article>
  );
}
