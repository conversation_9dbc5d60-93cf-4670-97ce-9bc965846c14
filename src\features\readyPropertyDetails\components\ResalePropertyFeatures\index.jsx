import {
  FiCheckCircle,
  FiHome,
  FiCalendar,
  FiMapPin,
  FiSquare,
  FiCheck,

} from "react-icons/fi";

function ResalePropertyFeatures({ property }) {
  if (!property) return null;

  // Render feature value
  const renderFeatureValue = (feature) => {
    switch (feature.type) {
      case "boolean":
        return feature.value ? (
          <span className="inline-flex items-center gap-1 px-3 py-1 text-primary rounded-full text-sm md:text-base font-semibold">
             Yes
          </span>
        ) : (
          <span className="inline-flex items-center gap-1 px-3 py-1  text-primary rounded-full bg-red-100 text-red-700 text-sm md:text-base font-semibold">
        No
          </span>
        );
      case "number":
        return (
          <span className="text-lg md:text-xl font-bold text-primary">
            {feature.value}
          </span>
        );
      default:
        return (
          <span className="text-lg md:text-xl font-bold text-primary">
            {feature.value}
          </span>
        );
    }
  };

  // Icon mapping
  const getFeatureIcon = (label) => {
    const iconMap = {
      "Property Type": <FiHome className="text-primary text-2xl" />,
      Bedrooms: <FiHome className="text-primary text-2xl" />,
      Bathrooms: <FiHome className="text-primary text-2xl" />,
      "Size (sqft)": <FiSquare className="text-primary text-2xl" />,
      Furnished: <FiCheck className="text-primary text-2xl" />,
      "Estimated Handover Date": <FiCalendar className="text-primary text-2xl" />,
    };
    return iconMap[label] || <FiCheckCircle className="text-primary text-2xl" />;
  };

  return (
    <section className="mb-10 h-full">
      <div className="bg-white rounded-2xl shadow-xl p-8 h-full flex flex-col">
        {/* Title */}
        <div className="flex items-center gap-3 mb-8">
          <FiCheckCircle className="text-3xl text-primary" />
          <h3 className="text-2xl md:text-3xl font-extrabold text-gray-900">
            Property Features
          </h3>
        </div>

        {/* Feature Grid */}
        <div className="grid sm:grid-cols-2 gap-6 flex-1 overflow-auto">
          {property.property_features?.map((feature, index) => (
            <div
              key={index}
              className="flex items-start gap-4 bg-gray-50 rounded-xl px-5 py-5 shadow-sm hover:shadow-md transition"
            >
              <div className="flex-shrink-0">{getFeatureIcon(feature.label)}</div>
              <div className="flex flex-col flex-1">
                <span className="text-sm md:text-base text-gray-500 uppercase tracking-wide">
                  {feature.label}
                </span>
                {renderFeatureValue(feature)}
              </div>
            </div>
          ))}

          {/* Location */}
          {property.location && (
            <div className="flex items-start gap-4 bg-gray-50 rounded-xl px-5 py-5 shadow-sm hover:shadow-md transition">
              <div className="flex-shrink-0">
                <FiMapPin className="text-primary text-2xl" />
              </div>
              <div className="flex flex-col flex-1">
                <span className="text-sm md:text-base text-gray-500 uppercase tracking-wide">
                  Location
                </span>
                <span className="text-lg md:text-xl font-bold text-primary">
                  {property.location.area?.name}, {property.location.city}
                </span>
              </div>
            </div>
          )}

          {/* Developer */}
          {property.developer && (
            <div className="flex items-start gap-4 bg-gray-50 rounded-xl px-5 py-5 shadow-sm hover:shadow-md transition">
              <div className="flex-shrink-0">
                <FiHome className="text-primary text-2xl" />
              </div>
              <div className="flex flex-col flex-1">
                <span className="text-sm md:text-base text-gray-500 uppercase tracking-wide">
                  Developer
                </span>
                <span className="text-lg md:text-xl font-bold text-primary">
                  {property.developer}
                </span>
              </div>
            </div>
          )}
        </div>
      </div>
    </section>
  );
}

export default ResalePropertyFeatures;
