import { create } from "zustand";
import axios from "axios";

const useServicesStore = create((set) => ({
  services: [],
  paginationInfo: null,
  loading: false,
  error: null,

  // Accept params for pageSize, pageNumber, and body
  fetchServices: async ({ pageSize = 50, pageNumber = 1, body = {} } = {}) => {
    set({ loading: true, error: null });
    try {
      const res = await axios.post(
        `${import.meta.env.VITE_API_BASE_URL}/api/services/get?pageSize=${pageSize}&pageNumber=${pageNumber}`,
        body
      );

      set({
        services: res.data.services,
        paginationInfo: res.data.paginationInfo,
        loading: false,
      });
    } catch (err) {
      set({
        error: err.message || "Failed to load services",
        loading: false,
      });
    }
  },
}));

export default useServicesStore;
