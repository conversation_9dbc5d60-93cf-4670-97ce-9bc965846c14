import { useState } from "react";
import { FiChevronDown } from "react-icons/fi";

const logoSrc = "/images/Logo.svg";

export default function Contact() {
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    phone: "",
    email: "",
  });

  const [answers, setAnswers] = useState({});
  const [formErrors, setFormErrors] = useState("");

  const questionsData = [
    {
      key: "country",
      question:
        "In which country are you currently based or holding residency?",
      type: "radio",
      options: [
        "Afghanistan",
        "Albania",
        "Algeria",
        "Andorra",
        "Angola",
        "Antigua and Barbuda",
        "Argentina",
        "Armenia",
        "Australia",
        "Austria",
        "Azerbaijan",
        "Bahamas",
        "Bahrain",
        "Bangladesh",
        "Barbados",
        "Belarus",
        "Belgium",
        "Belize",
        "Benin",
        "Bhutan",
        "Bolivia",
        "Bosnia and Herzegovina",
        "Botswana",
        "Brazil",
        "Brunei",
        "Bulgaria",
        "Burkina Faso",
        "Burundi",
        "Cabo Verde",
        "Cambodia",
        "Cameroon",
        "Canada",
        "Central African Republic",
        "Chad",
        "Chile",
        "China",
        "Colombia",
        "Comoros",
        "Congo, Democratic Republic of the",
        "Congo, Republic of the",
        "Costa Rica",
        "Croatia",
        "Cuba",
        "Cyprus",
        "Czech Republic",
        "Denmark",
        "Djibouti",
        "Dominica",
        "Dominican Republic",
        "Ecuador",
        "Egypt",
        "El Salvador",
        "Equatorial Guinea",
        "Eritrea",
        "Estonia",
        "Eswatini",
        "Ethiopia",
        "Fiji",
        "Finland",
        "France",
        "Gabon",
        "Gambia",
        "Georgia",
        "Germany",
        "Ghana",
        "Greece",
        "Grenada",
        "Guatemala",
        "Guinea",
        "Guinea-Bissau",
        "Guyana",
        "Haiti",
        "Honduras",
        "Hungary",
        "Iceland",
        "India",
        "Indonesia",
        "Iran",
        "Iraq",
        "Ireland",
        "Israel",
        "Italy",
        "Jamaica",
        "Japan",
        "Jordan",
        "Kazakhstan",
        "Kenya",
        "Kiribati",
        "Korea, North",
        "Korea, South",
        "Kosovo",
        "Kuwait",
        "Kyrgyzstan",
        "Laos",
        "Latvia",
        "Lebanon",
        "Lesotho",
        "Liberia",
        "Libya",
        "Liechtenstein",
        "Lithuania",
        "Luxembourg",
        "Madagascar",
        "Malawi",
        "Malaysia",
        "Maldives",
        "Mali",
        "Malta",
        "Marshall Islands",
        "Mauritania",
        "Mauritius",
        "Mexico",
        "Micronesia",
        "Moldova",
        "Monaco",
        "Mongolia",
        "Montenegro",
        "Morocco",
        "Mozambique",
        "Myanmar",
        "Namibia",
        "Nauru",
        "Nepal",
        "Netherlands",
        "New Zealand",
        "Nicaragua",
        "Niger",
        "Nigeria",
        "North Macedonia",
        "Norway",
        "Oman",
        "Pakistan",
        "Palau",
        "Panama",
        "Papua New Guinea",
        "Paraguay",
        "Peru",
        "Philippines",
        "Poland",
        "Portugal",
        "Qatar",
        "Romania",
        "Russia",
        "Rwanda",
        "Saint Kitts and Nevis",
        "Saint Lucia",
        "Saint Vincent and the Grenadines",
        "Samoa",
        "San Marino",
        "Sao Tome and Principe",
        "Saudi Arabia",
        "Senegal",
        "Serbia",
        "Seychelles",
        "Sierra Leone",
        "Singapore",
        "Slovakia",
        "Slovenia",
        "Solomon Islands",
        "Somalia",
        "South Africa",
        "South Sudan",
        "Spain",
        "Sri Lanka",
        "Sudan",
        "Suriname",
        "Sweden",
        "Switzerland",
        "Syria",
        "Taiwan",
        "Tajikistan",
        "Tanzania",
        "Thailand",
        "Timor-Leste",
        "Togo",
        "Tonga",
        "Trinidad and Tobago",
        "Tunisia",
        "Turkey",
        "Turkmenistan",
        "Tuvalu",
        "Uganda",
        "Ukraine",
        "United Arab Emirates",
        "United Kingdom",
        "United States",
        "Uruguay",
        "Uzbekistan",
        "Vanuatu",
        "Vatican City",
        "Venezuela",
        "Vietnam",
        "Yemen",
        "Zambia",
        "Zimbabwe",
      ],
    },
    {
      key: "preferredCommunication",
      question: "How would you like us to contact you?",
      type: "checkbox",
      options: ["WhatsApp", "Email", "Phone"],
    },
    {
      key: "investmentObjectives",
      question: "What are your main investment objectives?",
      type: "checkbox",
      options: [
        "Capital Appreciation",
        "Passive Income",
        "Portfolio Diversification",
        "Short-Term Gains",
      ],
    },
    {
      key: "investmentHorizon",
      question: "What is your preferred investment timeline?",
      type: "radio",
      options: ["Less than 1 year", "1–3 years", "3–5 years", "5+ years"],
    },
    {
      key: "investmentExperience",
      question: "What is your level of investment experience?",
      type: "radio",
      options: ["None", "Beginner", "Intermediate", "Advanced"],
    },
    {
      key: "investmentAmount",
      question: "What is your estimated investment amount?",
      type: "radio",
      options: [
        "Less than $50,000",
        "$50,000 - $250,000",
        "$250,000 - $1M",
        "More than $1M",
      ],
    },
    {
      key: "investmentFrequency",
      question: "How often do you plan to invest?",
      type: "radio",
      options: ["One-time", "Monthly", "Quarterly", "Annually"],
    },
    {
      key: "investmentRisk",
      question: "What is your risk tolerance?",
      type: "radio",
      options: ["Low", "Medium", "High"],
    },
    {
      key: "investmentKnowledge",
      question: "How would you rate your investment knowledge?",
      type: "radio",
      options: ["Basic", "Moderate", "Advanced"],
    },
    {
      key: "investmentGoals",
      question: "What are your primary investment goals?",
      type: "checkbox",
      options: [
        "Capital Preservation",
        "Income Generation",
        "Tax Optimization",
        "Diversification",
      ],
    },
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleAnswerChange = (questionKey, value) => {
    setAnswers((prev) => ({
      ...prev,
      [questionKey]: value,
    }));
  };

  const validateForm = () => {
    setFormErrors(""); // Clear old errors

    // Check all form fields filled
    for (const field of ["firstName", "lastName", "phone", "email"]) {
      if (!formData[field].trim()) {
        setFormErrors(`Please fill your ${field}.`);
        return false;
      }
    }

    // Check all questions answered
    for (const q of questionsData) {
      const ans = answers[q.key];
      if (q.type === "checkbox") {
        if (!Array.isArray(ans) || ans.length === 0) {
          setFormErrors(`Please answer: "${q.question}"`);
          return false;
        }
      } else {
        if (!ans) {
          setFormErrors(`Please answer: "${q.question}"`);
          return false;
        }
      }
    }

    setFormErrors("");
    return true;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!validateForm()) return;
    console.log("Form submitted:", { formData, answers });
    alert("Thank you! Your form has been submitted.");
    // Reset form or redirect here if needed
  };

  return (
    <div className="min-h-screen bg-[#F7F0EB] pt-55">
      {/* Top Section - no horizontal padding changes */}
      {/* <section className="bg-secondary py-20 px-6">
        <div className="mx-auto text-center">
          <div className="mb-8">
            <img
              src={logoSrc}
              alt="Lux Investments Logo"
              className="mx-auto min-h-96 w-auto"
            />
          </div>
        </div>
      </section> */}

      {/* <iframe
        src="https://api.luxinvest.io/widget/form/sUBq9c3lLy1DenEBqYIT"
        // style="width:100%;height:100%;border:none;border-radius:3px"
        id="inline-sUBq9c3lLy1DenEBqYIT"
        data-layout="{'id':'INLINE'}"
        data-trigger-type="alwaysShow"
        data-trigger-value=""
        data-activation-type="alwaysActivated"
        data-activation-value=""
        data-deactivation-type="neverDeactivate"
        data-deactivation-value=""
        data-form-name="Form 4"
        // data-height="741"
        data-layout-iframe-id="inline-sUBq9c3lLy1DenEBqYIT"
        data-form-id="sUBq9c3lLy1DenEBqYIT"
        title="Form 4"
      ></iframe> */}

      {/* <iframe
        src="/widget/form/Ii5fksG2zB4Cxned02DU"
        // style="width:100%;height:100%;border:none;border-radius:0px"
        id="inline-Ii5fksG2zB4Cxned02DU"
        data-layout="{'id':'INLINE'}"
        data-trigger-type="alwaysShow"
        data-trigger-value=""
        data-activation-type="alwaysActivated"
        data-activation-value=""
        data-deactivation-type="neverDeactivate"
        data-deactivation-value=""
        data-form-name="Luxinvestmentgroup.form"
        // data-height="1047"
        data-layout-iframe-id="inline-Ii5fksG2zB4Cxned02DU"
        data-form-id="Ii5fksG2zB4Cxned02DU"
        title="Luxinvestmentgroup.form"
      ></iframe> */}

      {/*        height:100%;border:none;border-radius:0px" */}
      <iframe
        style={{ width: "100%", color: "black !important" }}
        src="https://api.luxinvest.io/widget/form/Ii5fksG2zB4Cxned02DU"
        // style="width:100%"
        id="inline-Ii5fksG2zB4Cxned02DU"
        data-layout="{'id':'INLINE'}"
        data-trigger-type="alwaysShow"
        data-trigger-value=""
        data-activation-type="alwaysActivated"
        data-activation-value=""
        data-deactivation-type="neverDeactivate"
        data-deactivation-value=""
        data-form-name="Luxinvestmentgroup.form"
        // data-height="1047"
        data-layout-iframe-id="inline-Ii5fksG2zB4Cxned02DU"
        data-form-id="Ii5fksG2zB4Cxned02DU"
        title="Luxinvestmentgroup.form"
      ></iframe>

      {/* Welcome + Form - add horizontal padding on mobile */}
    </div>
  );
}
// <section className="mx-auto text-center py-16 px-6 sm:px-8 md:px-0">
//   {/* Welcome Text */}
//   <div className="mb-12">
//     <p className="text-gray-600 text-4xl font-outfit mb-4">Welcome to</p>
//     <h1 className="text-4xl md:text-6xl font-playfair font-bold text-primary mb-8 tracking-wide">
//       Lux Investments GROUP
//     </h1>
//     <p className="text-gray-700 text-3xl font-outfit max-w-4xl mx-auto leading-relaxed">
//       To ensure your capital is allocated to the most suitable and
//       rewarding opportunities, please complete the following form. Your
//       responses will guide our strategic recommendations.
//     </p>
//   </div>

//   {/* Contact Form */}
//   <form
//     onSubmit={handleSubmit}
//     className="mx-auto w-full max-w-4xl border-b-4 border-b-[#D4B78B]"
//   >
//     <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
//       {["firstName", "lastName", "phone", "email"].map((field) => (
//         <input
//           key={field}
//           type={
//             field === "email"
//               ? "email"
//               : field === "phone"
//               ? "tel"
//               : "text"
//           }
//           name={field}
//           value={formData[field]}
//           onChange={handleInputChange}
//           placeholder={field
//             .replace(/([A-Z])/g, " $1")
//             .replace(/^./, (str) => str.toUpperCase())}
//           required
//           className="w-full px-6 py-4 bg-[#E7D2B8] rounded-full text-gray-800 font-outfit text-lg placeholder-gray-600 focus:outline-none focus:ring-2 focus:ring-primary transition-all duration-300"
//         />
//       ))}
//     </div>

//     {/* Investment Questions */}
//     <div className="flex flex-col gap-8 pb-12">
//       {questionsData.map(({ key, question, options, type }) => (
//         <AccordionQuestion
//           key={key}
//           question={question}
//           options={options}
//           type={type}
//           selectedValue={answers[key]}
//           onChange={(value) => handleAnswerChange(key, value)}
//         />
//       ))}
//     </div>

//     {/* Form Errors */}
//     {formErrors && (
//       <p className="text-red-600 font-semibold mb-6 text-center">
//         {formErrors}
//       </p>
//     )}

//     {/* Submit Button */}
//     <div className="text-center mb-16">
//       <button
//         type="submit"
//         className="bg-[#231b41] hover:bg-[#180b47] text-white font-outfit font-semibold text-lg px-16 py-4 rounded-full transition-all duration-300 shadow-lg"
//       >
//         SUBMIT & CONNECT
//       </button>
//     </div>
//   </form>
// </section>

// {/* Final Section - add horizontal padding on mobile */}
// <section className="pb-10 border-b-8 border-[#D4B78B] px-6 sm:px-8 md:px-0">
//   <div className="container mx-auto">
//     <div className="max-w-3xl mx-auto text-center">
//       <p className="text-gray-800 text-xl font-outfit leading-relaxed mb-8">
//         Once submitted, our team will analyze your profile and present
//         tailored opportunities that align with your goals, risk appetite,
//         and capital strategy.
//       </p>
//     </div>
//   </div>
// </section>

// Accordion Question Component with improved UI for options
function AccordionQuestion({
  question,
  options,
  type,
  selectedValue,
  onChange,
}) {
  const [isOpen, setIsOpen] = useState(false);
  const isCheckbox = type === "checkbox";

  const handleCheckboxChange = (option) => {
    if (!Array.isArray(selectedValue)) {
      onChange([option]);
      return;
    }
    if (selectedValue.includes(option)) {
      onChange(selectedValue.filter((v) => v !== option));
    } else {
      onChange([...selectedValue, option]);
    }
  };

  return (
    <div className="border border-[#D4B78B] rounded-lg overflow-hidden shadow-md bg-white">
      {/* Question button */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full bg-[#E7D2B8] px-6 py-5 text-left font-outfit text-2xl text-gray-800 flex items-center justify-between hover:bg-[#DCC2A3] transition-colors duration-300 rounded-t-lg"
      >
        <span className="font-bold">{question}</span>
        {selectedValue && selectedValue.length > 0 && (
          <span className=" bg-green-500 p-2 text-white ml-auto mr-3  rounded-full text-sm">
            Answered
          </span>
        )}
        <FiChevronDown
          className={`w-6 h-6 text-gray-800 transform transition-transform duration-300 ${
            isOpen ? "rotate-180" : ""
          }`}
        />
      </button>
      {/* Options container */}
      <div
        className={`transition-all duration-500 ease-in-out overflow-hidden ${
          isOpen
            ? "max-h-[400px] opacity-100 py-4 px-6"
            : "max-h-0 opacity-0 py-0 px-6"
        }`}
      >
        <div className="flex flex-col gap-3 max-h-[300px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
          {options.map((option, index) => {
            const checked = isCheckbox
              ? Array.isArray(selectedValue) && selectedValue.includes(option)
              : selectedValue === option;

            return (
              <label
                key={`${option}-${index}`}
                className={`cursor-pointer select-none px-5 py-3 rounded-lg border transition-all duration-300 font-outfit font-semibold text-lg
                  ${
                    checked
                      ? "bg-primary text-white border-primary shadow-lg"
                      : "bg-white text-gray-800 border-gray-300 hover:bg-gray-100"
                  }`}
              >
                <input
                  type={type}
                  name={question}
                  value={option}
                  checked={checked}
                  onChange={() =>
                    isCheckbox ? handleCheckboxChange(option) : onChange(option)
                  }
                  className="hidden"
                />
                {option}
              </label>
            );
          })}
        </div>
      </div>{" "}
    </div>
  );
}
