// store/destinationStore.js
import { create } from "zustand";

const useProjectStore = create((set) => ({
  projects: [],
  filters: {
    destination: null,
    developer: null,
  },
  loading: false,
  error: null,

  setFilters: (filters) => set({ filters }),

  fetchProjects: async (filters = {}, pageSize) => {
    set({
      loading: true,
      error: null,
      filters,
    });

    try {
      const filterConstructor = {};

      // DESTINATION (single ID)
      if (filters.destination && filters.destination !== "all") {
        filterConstructor.destination = filters.destination;
      }

      // DEVELOPER
      if (filters.developer && filters.developer !== "all") {
        filterConstructor.developer = filters.developer;
      }

      let url = `${import.meta.env.VITE_API_BASE_URL}/api/projects/get`;
      if (pageSize) {
        url += `?pageSize=${pageSize}`;
      }

      console.debug("fetchProjects: POST body:", filterConstructor);

      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(filterConstructor),
      });

      if (!response.ok) {
        throw new Error(`Server error: ${response.status}`);
      }

      const data = await response.json();
      console.log(data);

      set({
        projects: data.projects || [],
        loading: false,
      });
    } catch (error) {
      console.error("❌ API Error:", error);
      set({
        error: error.message,
        loading: false,
      });
    }
  },
}));

export default useProjectStore;
