import { useEffect, useState } from "react";
import Skeleton from "react-loading-skeleton";
import { BsExclamationCircle } from "react-icons/bs";
import useServicesStore from "../../../store/servicesStore";
import constructUrl from "../../../utils/constructUrl";

export default function WhatWeOffer() {
  const { services, fetchServices, loading } = useServicesStore();
  const [selectedService, setSelectedService] = useState(null);
  const [imgLoading, setImgLoading] = useState(true);

  // Fetch services
  useEffect(() => {
    fetchServices();
  }, [fetchServices]);

  // Auto-select first service when available
  useEffect(() => {
    if (services.length > 0) {
      setSelectedService(services[0]);
    }
  }, [services]);

  /** --------------------
   * Loading State
   * -------------------- */
  if (loading) {
    return (
      <section className="mx-auto flex flex-col md:flex-row max-w-[120rem] h-[50rem] rounded-xl bg-[#F7F0EB] overflow-hidden p-6 sm:p-8 gap-6 animate-pulse">
        <div className="w-full md:w-1/3 space-y-4">
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
        </div>
        <div className="w-full md:w-2/3 flex flex-col gap-6">
          <Skeleton className="h-8 w-40" />
          <Skeleton className="h-24 w-full" />
          <Skeleton className="h-8 w-40" />
          <Skeleton className="h-16 w-full" />
          <Skeleton className="h-64 w-full rounded-lg" />
        </div>
      </section>
    );
  }

  /** --------------------
   * Empty State
   * -------------------- */
  if (!selectedService) {
    return (
      <section className="mx-auto flex flex-col items-center justify-center max-w-[120rem] h-[50rem] rounded-xl bg-[#F7F0EB] p-6 sm:p-8 text-center">
        <div className="bg-[#BEA15D]/20 p-6 rounded-full mb-6">
          <BsExclamationCircle className="text-[#BEA15D] text-5xl" />
        </div>
        <h2 className="text-3xl sm:text-4xl font-bold mb-4 text-gray-800">
          No Services Available
        </h2>
        <p className="text-lg sm:text-xl text-gray-600 max-w-lg mb-6">
          We currently don’t have any services to show. Please check back later.
        </p>
        <button
          onClick={fetchServices}
          className="px-6 py-3 rounded-lg bg-[#BEA15D] text-white font-semibold hover:bg-[#a88d50] shadow-md transition"
        >
          Retry
        </button>
      </section>
    );
  }

  /** --------------------
   * Normal State
   * -------------------- */
  return (
    <section
      className="mx-auto flex flex-col lg:flex-row 
      w-full max-w-[98.1875rem] h-auto lg:h-[55.875rem] 
      rounded-[1.0625rem] bg-[#F7F0EB] overflow-hidden"
    >
      {/* Left Column: Service list */}
      <div className="w-full lg:w-1/3 p-4 sm:p-8 lg:p-16 border-r border-[#CBC8C8] overflow-y-auto">
        <h2
          className="mb-16 font-playfair font-bold text-[#C1A15E] capitalize
          text-[1.5rem] sm:text-[2rem] md:text-[2.5rem] lg:text-[60px]
          leading-snug tracking-[-0.02em]"
        >
          What We Offer
        </h2>

        <ul className="space-y-2">
          {services.map((service, index) => (
            <li
              key={service._id}
              onClick={() => setSelectedService(service)}
              className={`flex items-center gap-3 cursor-pointer py-4 sm:py-6 px-1 border-b border-[#CBC8C8]
              font-outfit font-medium text-[0.95rem] sm:text-[1.1rem] md:text-[1.3rem] lg:text-[22px]
              leading-[150%] ${
                selectedService._id === service._id
                  ? "text-[#BEA15D]"
                  : "hover:text-[#BEA15D] text-[#12253F]"
              }`}
            >
              <span className="text-[#BEA15D]">
                {String(index + 1).padStart(2, "0")}
              </span>
              {service.name}
            </li>
          ))}
        </ul>
      </div>

      {/* Right Column: Selected service details */}
      <div className="w-full lg:w-2/3 p-4 sm:p-8 lg:p-16 flex flex-col gap-6 overflow-y-auto">
        {/* Overview */}
        <div>
          <h3 className="mb-2 font-[Outfit] font-medium text-[1.1rem] sm:text-[1.4rem] md:text-[1.8rem] lg:text-3xl">
            Overview
          </h3>
          <p className="font-[Outfit] font-light text-[1rem] sm:text-[1.25rem] md:text-[1.5rem] lg:text-2xl leading-snug">
            {selectedService.overview}
          </p>
        </div>

        {/* Details */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {selectedService.details.map((detail) => (
            <div key={detail._id}>
              <h4 className="mb-1 font-[Outfit] font-medium text-[1rem] sm:text-[1.2rem] md:text-[1.4rem] lg:text-xl">
                {detail.label}
              </h4>
              <p className="font-[Outfit] font-light text-[1rem] sm:text-[1.25rem] md:text-[1.5rem] lg:text-2xl leading-snug">
                {detail.value}
              </p>
            </div>
          ))}
        </div>

        {/* Image */}
        <div
          className="relative w-full h-[20rem] sm:h-[22rem] md:h-[24rem] 
          lg:w-[55.75rem] lg:h-[25.44rem] rounded-[0.8125rem] 
          overflow-hidden flex-shrink-0"
        >
          {imgLoading && (
            <Skeleton className="absolute inset-0 w-full h-full rounded-[0.8125rem] animate-pulse" />
          )}
          <img
            src={constructUrl(selectedService.img)}
            alt={selectedService.name}
            onLoad={() => setImgLoading(false)}
            className={`w-full h-full object-cover rounded-[0.8125rem] transition-opacity duration-300 ${
              imgLoading ? "opacity-0" : "opacity-100"
            }`}
          />
        </div>
      </div>
    </section>
  );
}
