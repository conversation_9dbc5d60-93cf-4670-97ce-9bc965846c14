import { useState } from "react";

function Card({
  title,
  imgSrc,
  subtitle,
  price,
  destination,
  tags,
  isDark = false,
  isAreasCovered = false,
  style = {},
}) {
  const [imgLoaded, setImgLoaded] = useState(false);

  return (
    <div className="md:text-center mb-8" style={style}>
      <div className="relative w-full min-h-[100px]">
        {/* Skeleton */}
        {!imgLoaded && (
          <div className="absolute inset-0 bg-gray-300 animate-pulse" />
        )}

        {/* Image */}
        <img
          src={imgSrc}
          alt={title || "Card image"}
          className={`w-full h-auto max-h-[591px] ${
            !isAreasCovered && "max-w-[551px]"
          } mx-auto object-cover transition-opacity duration-500 ${
            imgLoaded ? "opacity-100" : "opacity-0"
          }`}
          style={{ aspectRatio: "551/591" }}
          height={591}
          onLoad={() => setImgLoaded(true)}
        />

        {/* Tags */}
        {tags && imgLoaded && (
          <div className="absolute top-6 left-6 right-6 flex space-x-2 flex-wrap gap-4 max-w-full">
            {tags.map((tag) => (
              <span
                key={tag}
                className="bg-white opacity-75 p-2 backdrop-blur-xl py-1 px-6 text-lg font-outfit font-normal break-words"
              >
                {tag}
              </span>
            ))}
          </div>
        )}
      </div>

      <div className="text-center font-sans mt-6 md:text-left">
        <p
          className={`text-3xl ${
            isAreasCovered ? "font-outfit" : "font-playfair"
          }`}
        >
          {title}
        </p>
        <p className="text-gray-500 font-outfit text-2xl">{subtitle}</p>
        {(price || destination) && (
          <p
            className={`text-2xl mt-2 text-[#878787] ${
              isDark && "text-primary"
            }`}
          >
            {destination ? destination : price}
          </p>
        )}
      </div>
    </div>
  );
}

export default Card;
