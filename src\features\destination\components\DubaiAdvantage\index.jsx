import Subtitle from "../../../../components/Subtitle/index";
const firstImg = "/images/DA_1.webp";
import { useState } from "react";

function DubaiAdvatage() {
  const [options, setOptions] = useState(
    ["Overview", "Urban Landscape", "Landmarks"].map((o, i) => ({
      name: o,
      isChoosen: !i,
    }))
  );

  function hanldeOptionChange(e) {
    setOptions((oldOptions) =>
      [...oldOptions].map((o) => ({
        ...o,
        isChoosen: e.target.value === o.name,
      }))
    );
  }

  return (
    <section className="py-20 bg-secondary text-white">
      <div className="container mx-auto px-4">
        <Subtitle text="The Dubai Advantage" />
        <div className="grid md:grid-cols-12 gap-4 lg:gap-5">
          <h2 className="md:col-span-8 text-2xl sm:text-3xl lg:text-4xl xl:text-5xl leading-tight">
            Dubai: The Smart Choice for Investors
          </h2>
        </div>

        <div className="mt-8 lg:mt-12 xl:mt-15 flex flex-col xl:flex-row justify-center xl:justify-start gap-8 lg:gap-12 xl:gap-16 w-full">
          <div className="flex justify-center xl:justify-start">
            <img
              src={firstImg}
              className="object-cover w-full max-w-sm sm:max-w-md lg:max-w-2xl xl:max-w-3xl max-h-[300px] sm:max-h-[400px] lg:max-h-[500px] xl:max-h-[526px]"
              style={{ aspectRatio: "741/526" }}
              alt="Dubai"
            />
          </div>
          <div className="text-center xl:text-start w-full xl:max-w-[400px] flex flex-col items-center xl:items-start gap-6 lg:gap-8 px-4 xl:px-0">
            <div className="flex justify-center xl:justify-start gap-2 sm:gap-3 lg:gap-4 flex-wrap">
              {options.map((option) => (
                <button
                  key={option.name}
                  value={option.name}
                  onClick={hanldeOptionChange}
                  className={`py-1.5 sm:py-2 px-3 sm:px-4 lg:px-6 bg-transparent text-nowrap text-sm sm:text-base lg:text-lg xl:text-xl border border-white rounded-full hover:bg-primary transition-colors ${
                    option.isChoosen && "bg-primary !border-primary"
                  }`}
                >
                  {option.name}
                </button>
              ))}
            </div>
            <p className="font-sans text-sm sm:text-base lg:text-lg leading-relaxed">
              Dubai is the most dynamic and prosperous city in the UAE, offering
              a unique blend of innovation, luxury, and opportunity. Its
              thriving economy and investor-friendly policies make it a global
              magnet for entrepreneurs and high-net-worth individuals. With no
              income tax, strong government support, and world-class
              infrastructure, Dubai stands out as a premier destination for
              real estate investment and elevated living.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}

export default DubaiAdvatage;
