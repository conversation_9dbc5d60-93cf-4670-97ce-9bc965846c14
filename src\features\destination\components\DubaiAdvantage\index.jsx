import Subtitle from "../../../../components/Subtitle/index";
const firstImg = "/images/DA_1.webp";
import { useState } from "react";

function DubaiAdvatage() {
  const [options, setOptions] = useState(
    ["Overview", "Urban Landscape", "Landmarks"].map((o, i) => ({
      name: o,
      isChoosen: !i,
    }))
  );

  function hanldeOptionChange(e) {
    setOptions((oldOptions) =>
      [...oldOptions].map((o) => ({
        ...o,
        isChoosen: e.target.value === o.name,
      }))
    );
  }

  return (
    <section className="py-20 bg-secondary text-white">
      <div className="container mx-auto">
        <Subtitle text="The Dubai Advantage" />
        <div className="grid md:grid-cols-12 gap-5">
          <h2 className="md:col-span-8 text-5xl">
            Dubai: The Smart Choice for Investors
          </h2>
        </div>

        <div className="mt-15 flex flex-wrap justify-center gap-16 w-full">
          <img
            src={firstImg}
            className="object-cover w-full max-h-[526px] max-w-3xl"
            style={{ aspectRatio: "741/526" }}
            alt="Dubai"
          />
          <div className="m-auto text-center xl:text-start w-full xl:max-w-1/3 flex flex-col items-start gap-8">
            <div className="flex mx-auto xl:mx-0 gap-4 flex-wrap">
              {options.map((option) => (
                <button
                  key={option.name}
                  value={option.name}
                  onClick={hanldeOptionChange}
                  className={`py-2 px-6 bg-transparent text-nowrap text-xl border-1 rounded-4xl hover:bg-primary ${
                    option.isChoosen && "bg-primary !border-0"
                  }`}
                >
                  {option.name}
                </button>
              ))}
            </div>
            <p className="font-sans text-lg">
              Dubai is the most dynamic and prosperous city in the UAE, offering
              a unique blend of innovation, luxury, and opportunity. Its
              thriving economy and investor-friendly policies make it a global
              magnet for entrepreneurs and high-net-worth individuals. With no
              income tax, strong government support, and world-class
              infrastructure, Dubai stands out as a premier destinati on for
              real estate investment and elevated living.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}

export default DubaiAdvatage;
