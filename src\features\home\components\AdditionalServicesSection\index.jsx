import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { FiArrowRight } from "react-icons/fi";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import useServicesStore from "../../../../store/servicesStore";
import constructUrl from "../../../../utils/constructUrl";

export default function AdditionalServices() {
  const { services, loading, fetchServices } = useServicesStore();
  const [activeCard, setActiveCard] = useState(null);
  const [loadedImages, setLoadedImages] = useState({});
  const navigate = useNavigate();
  useEffect(() => {
    fetchServices({
      pageSize: 4,
      body: { isFeatured: true },
    });
  }, [fetchServices]);

  const handleClick = () => {
    // always navigate, regardless of touch or desktop
    navigate(`/services`);
  };

  const toggleCard = (id) => setActiveCard((prev) => (prev === id ? null : id));
  const handleMouseEnter = (id) => setActiveCard(id);
  const handleMouseLeave = () => setActiveCard(null);

  const truncateOverview = (text, wordLimit = 60) => {
    if (!text) return "";
    const words = text.split(/\s+/);
    return words.length > wordLimit
      ? words.slice(0, wordLimit).join(" ") + "..."
      : text;
  };

  return (
    <section className="w-full bg-[#f5f1ee] py-20">
      <div className="container max-w-[1926px] mx-auto">
        <h2 className="font-[500] text-[28px] leading-[40px] sm:text-[36px] sm:leading-[48px] xl:text-[44px] xl:leading-[60px] 2xl:text-[55px] 2xl:leading-[68px] text-center tracking-[-0.025em]">
          We are not just a real estate brokerage company
          <br />
          we also offer a range of{" "}
          <span className="italic text-[#BEA15D] font-semibold lining-nums proportional-nums">
            additional services!
          </span>
        </h2>

        <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 mt-16 xl:mt-24 xl:divide-x xl:divide-gray-300 gap-6">
          {loading
            ? Array.from({ length: 4 }).map((_, i) => (
                <div
                  key={i}
                  className="relative h-[300px] sm:h-[320px] xl:h-[350px] overflow-hidden transition-all duration-700 ease-in-out group cursor-pointer"
                >
                  <Skeleton height="50%" />
                  <Skeleton count={3} style={{ marginTop: "8px" }} />
                  <Skeleton height="50%" />
                </div>
              ))
            : services.map((service, id = 1) => {
                const isActive = activeCard === service._id;

                return (
                  <div
                    key={service._id}
                    className={`relative h-[300px] sm:h-[320px] xl:h-[400px] overflow-hidden transition-all duration-700 ease-in-out group cursor-pointer`}
                    onClick={() => handleClick(service._id)} // navigate on click
                    onMouseEnter={() => handleMouseEnter(service._id)}
                    onMouseLeave={handleMouseLeave}
                  >
                    {/* Top Content */}
                    <div
                      className={`absolute inset-0 flex flex-col text-left justify-center px-4 transition-all duration-700 ease-in-out
                        ${isActive ? "h-1/2 top-0 bg-[#BEA15D]" : "h-full"}`}
                    >
                      <div
                        className={`transition-all duration-700 ease-in-out flex flex-col justify-center ${
                          isActive ? "translate-y-4" : "translate-y-5"
                        }`}
                      >
                        <p
                          className={`font-[Outfit] text-[16px] sm:text-[18px] xl:text-[20px] font-normal leading-[24px] xl:leading-[30px] tracking-[-0.04em] mb-2 transition-all duration-700 ease-in-out ${
                            isActive ? "text-white scale-90" : "text-[#1e1e1e]"
                          }`}
                        >
                          {id + 1}
                        </p>
                        <h3
                          className={`text-[19px] font-normal  tracking-[-0.04em] whitespace-pre-line transition-all duration-700 ease-in-out  xl:text-4xl ${
                            isActive ? "text-white scale-90" : "text-[#1e1e1e]"
                          }`}
                        >
                          {service.name}
                        </h3>
                        <p
                          className={`mt-2 ml-5 font-[Outfit] text-[17px] sm:text-[18px] font-light italic leading-[22px] tracking-[-0.02em] w-full max-w-[90%] transition-opacity duration-700 ease-in-out ${
                            isActive
                              ? "opacity-100 text-white"
                              : "opacity-0 text-white"
                          }`}
                        >
                          {truncateOverview(service.overview, 15)}
                        </p>
                      </div>

                      <div
                        className={`mt-4 transition-opacity duration-500 ease-in-out ${
                          isActive ? "opacity-0" : "opacity-100 text-[#BEA15D]"
                        }`}
                      >
                        <FiArrowRight
                          style={{ width: "26.62px", height: "26.61px" }}
                        />
                      </div>
                    </div>

                    {/* Bottom Image */}
                    {loadedImages[service._id] ? (
                      <div
                        className={`absolute left-0 w-full h-1/2 bg-cover bg-center transition-all duration-700 ease-in-out ${
                          isActive ? "bottom-0" : "bottom-[-50%]"
                        }`}
                        style={{
                          backgroundImage: `url(${constructUrl(service.img)})`, // use service.img directly
                        }}
                      ></div>
                    ) : (
                      <div
                        className={`absolute left-0 w-full h-1/2 bg-cover bg-center transition-all duration-700 ease-in-out ${
                          isActive ? "bottom-0" : "bottom-[-50%]"
                        }`}
                      >
                        {" "}
                        <Skeleton height="100%" />
                      </div>
                    )}

                    {/* Preload image */}
                    <img
                      src={constructUrl(service.img)}
                      alt={service.name}
                      className="hidden"
                      onLoad={() =>
                        setLoadedImages((prev) => ({
                          ...prev,
                          [service._id]: true,
                        }))
                      }
                    />
                  </div>
                );
              })}
        </div>
      </div>
    </section>
  );
}
