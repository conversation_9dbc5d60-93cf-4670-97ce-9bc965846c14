const aboutImg = "/images/A_1.webp";
const secondAboutImg = "/images/CSP_2.webp";

const companies = [
  "OMNIYAT",
  "EAGLE HILLS",
  "SAMANA",
  "EMAAR",
  "SOBHA",
  "IMTIAZ",
];

const values = [
  "Lux Builds Trust",
  "Client-Centric",
  "High returns on investment",
  "Flexible payment plans",
];

function Intro() {
  return (
    <section className="py-20 bg-secondary text-white pt-55">
      <div className="container container-tiny mx-auto">
        <div className="grid xl:grid-cols-12 gap-15 relative">
          <div className="md:col-span-8 flex flex-col gap-30">
            <h2 className=" text-6xl leading-tight">
              Empowering Your Real Estate Journey: The Vision and Values Behind
              Lux Investments
            </h2>
            <p className="text-2xl font-light font-outfit w-[70%]">
              At Lux Investments, we don’t just sell real estate — we build
              long-term value and trust. Based in the heart of Dubai, our
              consultancy was founded with a clear mission: to guide local and
              international clients through the city’s dynamic property market
              with confidence, transparency, and strategic insight.
            </p>
            <img
              src={aboutImg}
              alt="About us"
              className="object-cover w-[80%]"
            />
          </div>

          <div className="xl:col-span-4 justify-self-end hidden xl:block">
            <div className="absolute bottom-5 right-0">
              <a className="flex items-center gap-2 pointer" href="#vision">
                <p className="font-outfit text-2xl">Scroll</p>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="currentColor"
                  className="bi bi-arrow-down inline-block size-6"
                  viewBox="0 0 16 16"
                >
                  <path
                    fill-rule="evenodd"
                    d="M8 1a.5.5 0 0 1 .5.5v11.793l3.146-3.147a.5.5 0 0 1 .708.708l-4 4a.5.5 0 0 1-.708 0l-4-4a.5.5 0 0 1 .708-.708L7.5 13.293V1.5A.5.5 0 0 1 8 1"
                  />
                </svg>
              </a>
              <img
                src={secondAboutImg}
                alt="About us"
                className="size-80 object-cover mt-14"
              />
            </div>
          </div>
        </div>

        {/* Vision */}
        <div id="vision">
          <h2 className="text-6xl leading-tight mt-40">
            We use,
            <span className="pl-4 text-primary italic font-bold">
              Real Estate
            </span>{" "}
            to
            <br /> show our appreciation of the world.
          </h2>

          <div className="mt-30">
            <div className="flex flex-wrap gap-y-10 gap-x-20">
              <div>
                <h2 className="font-outfit text-4xl">300 + Clients</h2>
                <p className="font-sans text-gray font-normal">
                  Served globally with personalized strategies
                </p>
              </div>
              <div>
                <h2 className="font-outfit text-4xl">$120M+ Secured</h2>
                <p className="font-sans text-gray font-normal">
                  In total investment and asset transactions
                </p>
              </div>
            </div>

            <div className="line !w-full !border-white my-10"></div>

            <ul className="flex flex-wrap justify-between gap-8">
              {companies.map((c) => (
                <li
                  className="m-auto text-3xl font-outfit font-light text-nowrap"
                  key={c}
                >
                  {c}
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Our Values */}
        <div className="mt-50 mb-32">
          <h2 className="text-primary text-6xl font-bold">Our Values</h2>
          <div className="mt-18 grid lg:grid-cols-12 gap-15">
            <ul className="flex gap-5 flex-col lg:col-span-4">
              {values.map((v, i) => (
                <li
                  key={v}
                  className="font-outfit font-normal text-2xl text-nowrap"
                >
                  <span className="mr-4 text-[#D2CAB7]">0{++i}</span>
                  <p className="inline-block">{v}</p>
                </li>
              ))}
            </ul>
            <p className="font-outfit font-light text-3xl lg:col-span-8 self-center justify-self-start lg:justify-self-center w-[75%]">
              Our end-to-end service gives us a complete view of each project,
              enabling seamless, high-impact retail solutions that drive results
              for brands worldwide.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}

export default Intro;
