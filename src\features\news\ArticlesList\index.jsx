import { MdArrowForward } from "react-icons/md";
import { BsArrowUpRight } from "react-icons/bs";
import { Link } from "react-router-dom";
import useNewsStore from "../../../store/newsStore";
import { useEffect, useState } from "react";
import constructUrl from "../../../utils/constructUrl";

export default function ArticlesList() {
  const { articles, articleLoading, fetchArticles } = useNewsStore();
  const [loadedImages, setLoadedImages] = useState({});

  useEffect(() => {
    fetchArticles();
  }, [fetchArticles]);

  const handleImageLoad = (idx) => {
    setLoadedImages((prev) => ({ ...prev, [idx]: true }));
  };

  /** ------------------
   * Empty State UI
   * ------------------ */
  if (!articleLoading && (!articles || articles.length === 0)) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh] bg-secondary text-white px-6 py-20">
        {/* Icon */}
        <div className="bg-[#bea15d]/20 rounded-full p-6 mb-6">
          <MdArrowForward className="text-[#BEA15D] text-4xl" />
        </div>

        {/* Heading */}
        <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white text-center">
          No Articles Available
        </h2>

        {/* Description */}
        <p className="text-lg md:text-xl text-gray-200 text-center max-w-lg mb-6">
          We currently don’t have any articles to show. Please check back later or explore other sections of our site.
        </p>

        {/* Optional CTA */}
        <button
          onClick={fetchArticles}
          className="flex items-center gap-2 px-6 py-3 rounded-lg bg-[#BEA15D] text-white font-semibold hover:bg-[#a88d50] transition-shadow shadow-md"
        >
          Refresh
          <BsArrowUpRight className="text-white text-lg" />
        </button>
      </div>
    );
  }

  return (
    <section className="bg-secondary py-12 px-4">
      <div className="mx-auto">
        {/* Section header */}
        <div className="flex items-center justify-between border-b border-white/30 pb-4 mb-8">
          <h2 className="flex items-center gap-2 font-[Playfair_Display] font-medium text-2xl sm:text-3xl text-white">
            Articles
          </h2>
        </div>

        {/* Articles grid */}
        {articleLoading ? (
          // Skeleton loading state
          <div className="grid gap-15 sm:grid-cols-2 lg:grid-cols-3 animate-pulse">
            {Array.from({ length: 6 }).map((_, idx) => (
              <div key={idx} className="flex flex-col">
                <div className="w-full h-64 sm:h-72 rounded-lg bg-gray-500/30" />
                <div className="mt-4 h-6 w-3/4 bg-gray-500/30 rounded" />
              </div>
            ))}
          </div>
        ) : (
          <div className="grid gap-15 sm:grid-cols-2 lg:grid-cols-3">
            {articles.map((article, idx) => (
              <div key={idx} className="flex flex-col">
                {/* Image with lazy + skeleton and click */}
                <Link to={`/articles/${article._id}`}>
                  <div className="w-full h-64 sm:h-72 rounded-lg relative overflow-hidden">
                    {!loadedImages[idx] && (
                      <div className="absolute inset-0 bg-gray-500/30 animate-pulse rounded-lg" />
                    )}
                    <img
                      src={constructUrl(article.img)}
                      alt={article.title}
                      loading="lazy"
                      onLoad={() => handleImageLoad(idx)}
                      className={`w-full h-full object-cover transition-transform duration-300 hover:scale-105 rounded-lg ${
                        loadedImages[idx] ? "opacity-100" : "opacity-0"
                      }`}
                    />
                  </div>
                </Link>

                {/* Title with click */}
                <Link
                  to={`/articles/${article._id}`}
                  className="mt-4 font-[Playfair_Display] font-medium text-lg sm:text-xl text-white flex justify-between hover:text-primary transition-colors"
                >
                  {article.title}
                  <MdArrowForward className="text-white text-2xl" />
                </Link>
              </div>
            ))}
          </div>
        )}
      </div>
    </section>
  );
}
