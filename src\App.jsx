import { BrowserRouter as Router, Routes, Route, useLocation, Navigate } from "react-router-dom";
import { useEffect } from "react";
import Header from "./layout/Header/Header";

import Home from "./pages/Home";
import Destinations from "./pages/Destinations";
import About from "./pages/About";
import PropertyDetails from "./pages/OffPlanPropertyDetails";
import ReadyUnitDetails from "./pages/ResaleUnitDetails";
import Contact from "./pages/Contact";

// Parent pages
import News from "./pages/News";
import Services from "./pages/Services";

// New detail pages

import ArticleDetails from "./pages/ArticleDetails";
import NewsDetails from "./pages/NewsDetails";

function ScrollToTop() {
  const { pathname } = useLocation();

  useEffect(() => {
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: "smooth", // 👈 this makes it smooth
    });
  }, [pathname]);

  return null;
}


function App() {
  return (
    <Router>
      <ScrollToTop />
      <div className="App" id="top">
        <main>
          <Routes>
            {/* Home */}
            <Route
              path="/"
              element={
                <>
                  <Header />
                  <Home />
                </>
              }
            />

            {/* About */}
            <Route
              path="/about"
              element={
                <>
                  <Header isDark />
                  <About />
                </>
              }
            />

            {/* News (parent) */}
            <Route
              path="/news"
              element={
                <>
                  <Header isDark />
                  <News />
                </>
              }
            />
            <Route
              path="/articles/:id"
              
              element={
                <>
                  <ArticleDetails />
                </>
              }
            />

            {/* Services */}
            <Route
              path="/services"
              element={
                <>
                  <Header />
                  <Services />
                </>
              }
            />
            <Route
              path="/news/:id"
              element={
                <>
                  <NewsDetails />
                </>
              }
            />

            {/* Contact */}
            <Route
              path="/contact"
              element={
                <>
                  <Header />
                  <Contact />
                </>
              }
            />

            {/* Destinations */}
            <Route
              path="/destinations"
              element={
                <>
                  <Header />
                  <Destinations />
                </>
              }
            />

            {/* Off-plan property details */}
            <Route
              path="/offPlan-Property/:id"
              caseSensitive={false}
              element={
                <>
                  <Header />
                  <PropertyDetails />
                </>
              }
            />

            {/* Resale property details */}
            <Route
              path="/resale-Property/:id"
              caseSensitive={false}
              element={
                <>
                  <Header />
                  <ReadyUnitDetails />
                </>
              }
            />

            {/* Catch-all */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </main>
      </div>
    </Router>
  );
}

export default App;