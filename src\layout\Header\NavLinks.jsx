import { NavLink, useLocation } from "react-router-dom";
import styles from "./Header.module.css";
// Import social media icons
const linkedinIcon = "/icons/linkedin.svg";
const facebookIcon = "/icons/facebook.svg";
const instagramIcon = "/icons/instagram.svg";

const links = [
  { to: "/", label: "Home" },
  { to: "/about", label: "About Us" },
  { to: "/destinations?type=resale&maxPrice=any", label: "Destinations" },
  { to: '/services',  label: 'Our Services' },
  { to: '/news',      label: 'News' },
  { to: "/contact", label: "Contact" },
];

const socialIcons = [
  { icon: linkedinIcon, href: "https://www.linkedin.com/company/*********/admin/dashboard/", alt: "LinkedIn" },
  { icon: facebookIcon, href: "https://www.facebook.com/profile.php?id=61578043092154", alt: "Facebook" },
  { icon: instagramIcon, href: "https://www.instagram.com/lux__investments/", alt: "Instagram" },
];

export default function NavLinks({ onLinkClick }) {
  const location = useLocation();
  const isAboutPage = location.pathname === "/about";
  const isNewsPage = location.pathname === "/news";
  const isServicesPage = location.pathname === "/services";
  return (
    <div className={styles.navContainer}>
      {/* Navigation Links - Left Side */}
      <ul className={styles.linkList}>
        {links.map(({ to, label }) => (
          <li key={to}>
            <NavLink
              to={to}
              onClick={onLinkClick}
              className={({ isActive }) => {
                if (isActive && isAboutPage ||isActive&&isNewsPage ||isActive&&isServicesPage) {
                  return styles.activeLink; // Active link on About page
                } else if (!isActive && isAboutPage ||!isActive&&isNewsPage ||! isActive&&isServicesPage) {
                  return `${styles.link} ${styles.whiteText}`; // Not active but on About
                } else if (isActive) {
                  return styles.activeLink; // Active link on other pages
                } else {
                  return styles.link; // Default link
                }
              }}
            >
              {label}
            </NavLink>
          </li>
        ))}
      </ul>

      {/* Social Icons - Right Side */}
      <ul className={styles.socialList}>
        {socialIcons.map(({ icon, href, alt }, i) => (
          <li key={i}>
            <a
              href={href}
              target="_blank"
              rel="noopener noreferrer"
              className={styles.socialLink}
            >
              <img src={icon} alt={alt} className={styles.socialIcon} />
            </a>
          </li>
        ))}
      </ul>
    </div>
  );
}
