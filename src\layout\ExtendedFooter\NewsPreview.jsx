import { useEffect } from "react";
import useNewsStore from "../../store/newsStore";
import { useNavigate } from "react-router-dom";

function NewsPreview() {
  const { news, newsLoading, fetchNews, error } = useNewsStore();

  useEffect(() => {
    fetchNews(3);
  }, [fetchNews]);

  const navigate = useNavigate(); // ✅ hook

  // Skeleton loading (matches card UI)
  if (newsLoading) {
    return (
      <section className="pb-20 pt-14 bg-secondary text-white">
        <div className="container mx-auto">
          {/* Responsive headline */}
          <h2 className="text-2xl sm:text-3xl md:text-5xl font-normal leading-snug sm:leading-tight md:leading-[3.5rem]">
            Latest real estate news, tips,
            <br />
            <span className="text-primary italic">and market updates</span>
          </h2>

          <ul className="mt-10 sm:mt-14 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
            {Array.from({ length: 3 }).map((_, idx) => (
              <li
                key={idx}
                className="border border-white rounded-3xl px-4 sm:px-6 py-6 sm:py-8 flex flex-col animate-pulse h-full"
              >
                <div className="h-5 sm:h-6 w-3/4 bg-gray-700 rounded mb-4 sm:mb-6"></div>
                <div className="space-y-2 sm:space-y-3 flex-1">
                  <div className="h-4 bg-gray-700 rounded w-full"></div>
                  <div className="h-4 bg-gray-700 rounded w-5/6"></div>
                  <div className="h-4 bg-gray-700 rounded w-2/3"></div>
                </div>
                <div className="flex gap-2 sm:gap-3 mt-4 sm:mt-6">
                  <div className="h-5 sm:h-6 w-16 sm:w-20 bg-gray-700 rounded-2xl"></div>
                  <div className="h-5 sm:h-6 w-20 sm:w-24 bg-gray-700 rounded-2xl"></div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="h-[60vh] flex items-center justify-center bg-secondary text-white">
        <p className="text-red-400 text-base sm:text-lg">{error}</p>
      </section>
    );
  }

  if (!news || news.length === 0) {
    return (
      <section className="h-[60vh] flex items-center justify-center bg-secondary text-white">
        <p className="text-gray-300 text-base sm:text-lg">No news available.</p>
      </section>
    );
  }

  return (
    <section className="pb-20 pt-14 bg-secondary text-white">
      <div className="container mx-auto">
        {/* Responsive headline */}
        <h2 className="text-2xl sm:text-3xl md:text-5xl font-normal leading-snug sm:leading-tight md:leading-[3.5rem]">
          Latest real estate news, tips,
          <br />
          <span className="text-primary italic">and market updates</span>
        </h2>

        <ul className="mt-10 sm:mt-14 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-12 items-stretch">
          {news.map(({ _id, title, content, createdAt, readingTime }) => {
            const formattedDate = new Date(createdAt).toLocaleDateString(
              "en-US",
              {
                year: "numeric",
                month: "short",
                day: "numeric",
              }
            );
            return (
              <li
                onClick={() => navigate(`/news/${_id}`)}
                key={_id}
                className="group relative border border-white rounded-3xl px-4 sm:px-6 py-6 sm:py-8 flex flex-col h-full
             cursor-pointer transition-all duration-300 ease-in-out
             hover:shadow-2xl hover:-translate-y-2 hover:border-primary/70"
              >
                <h3 className="text-lg sm:text-2xl">{title}</h3>
                <p className="mt-4 sm:mt-8 sm:mb-4 text-gray text-sm sm:text-lg font-sans line-clamp-3 flex-1">
                  {content}
                </p>
                <div className="mt-4 sm:mt-6 flex flex-wrap gap-2 sm:gap-3 justify-start">
                  <span className="bg-primary rounded-4xl py-1 px-3 sm:px-4 text-xs sm:text-sm md:text-base">
                    {readingTime} min read
                  </span>
                  <span className="bg-primary rounded-4xl py-1 px-3 sm:px-4 text-xs sm:text-sm md:text-base">
                    {formattedDate}
                  </span>
                </div>
              </li>
            );
          })}
        </ul>
      </div>
    </section>
  );
}

export default NewsPreview;
