import { create } from 'zustand';

const useNewsStore = create((set) => ({
  articles: [],
  news: [],
  newsLoading: false,
  articleLoading: false,
  error: null,

  fetchNews: async (pageSize = 40) => {
    set({ newsLoading: true, error: null });
    try {
      const res = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/articles/get?pageSize=${pageSize}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ type: "news" }),
      });

      if (!res.ok) {
        throw new Error('Failed to fetch News');
      }

      const data = await res.json();
      console.log(data);
      set({ news: data.articles || [], newsLoading: false });
    } catch (err) {
      console.error('Failed to fetch News:', err);
      set({ news: [], newsLoading: false, error: err.message });
    }
  },


  

  fetchArticles: async () => {
    set({ articleLoading: true, error: null });
    try {
      const res = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/articles/get`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ type: "article" }),
      });

      if (!res.ok) {
        throw new Error('Failed to fetch Articles');
      }

      const data = await res.json();
      console.log(data);
      set({ articles: data.articles || [], articleLoading: false });
    } catch (err) {
      console.error('Failed to fetch Articles:', err);
      set({ articles: [], articleLoading: false, error: err.message });
    }
  },
}));

export default useNewsStore;