const logoSrc = "/images/Logo.svg";
// const telegramIcon = "/icons/telegram.svg";
const whatsappIcon = "/icons/whatsapp.svg";
// import { useState } from "react";

const socialIcons = [
  // { icon: telegramIcon, href: "https://t.me/YourTelegramUsername", alt: "Telegram" },
  { icon: whatsappIcon, href: "https://wa.me/971529940884", alt: "WhatsApp" },
];

function Email() {
  return (
    <section id="contact" className="py-10 bg-secondary text-[#12253F] h-full">
      <div className="container mx-auto h-full">
        <div className="bg-[#F7F0EB] p-8 sm:p-12 md:p-20 text-center flex justify-center flex-col items-center gap-5 rounded-xl">
          {/* Logo */}
          <img
            className="size-20 sm:size-24 md:size-30 text-center"
            src={logoSrc}
            alt="Lux Investments"
          />

          {/* Headline with responsive scaling */}
          <p
            className="
              text-2xl sm:text-3xl md:text-5xl
              font-medium
              max-w-3xl
              leading-snug sm:leading-tight md:leading-[3.5rem]
            "
          >
            “Let <span className="font-bold italic">the experts</span> help you
            make
            <span className="font-bold italic"> the right investment</span>”
          </p>

          {/* Social links */}
          <div className="self-start text-start mt-12 w-full font-sans">
            <ul>
              {socialIcons.map(({ icon, href, alt }) => (
                <li
                  key={alt}
                  className="inline-block mr-6 sm:mr-8 md:mr-10 mt-2.5"
                >
                  <a
                    href={href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center"
                  >
                    <span className="mr-3 inline-flex items-center justify-center bg-primary p-2 sm:p-3 rounded-full">
                      <img
                        src={icon}
                        alt={alt}
                        className="block size-4 sm:size-5"
                      />
                    </span>
                    <span className="text-sm sm:text-base md:text-lg">
                      {alt}
                    </span>
                  </a>
                </li>
              ))}
            </ul>

            {/* External form iframe */}
            <div className="mt-12 w-full">
              <iframe
                src="https://api.luxinvest.io/widget/form/4zXm1D1DEZ0SjUHNYcMt"
                className="h-fit"
                style={{
                  width: "100%",
                  marginTop: "2rem",
                  backgroundColor: "transparent",
                }}
                id="inline-4zXm1D1DEZ0SjUHNYcMt"
                data-layout="{'id':'INLINE'}"
                data-trigger-type="alwaysShow"
                data-trigger-value=""
                data-activation-type="alwaysActivated"
                data-activation-value=""
                data-deactivation-type="neverDeactivate"
                data-deactivation-value=""
                data-form-name="Form 5"
                data-layout-iframe-id="inline-4zXm1D1DEZ0SjUHNYcMt"
                data-form-id="4zXm1D1DEZ0SjUHNYcMt"
                title="Form 5"
              ></iframe>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

function TextInput({ label, type, value, handleFormChange }) {
  return (
    <div className="relative lg:min-w-60">
      <input
        required
        type={type || "text"}
        id={label}
        onChange={handleFormChange}
        value={value}
        placeholder=" "
        className="
          peer w-full border-0 border-b-2 border-gray-300 bg-transparent px-0 py-2
          text-sm sm:text-base md:text-lg
          text-gray-900 placeholder-transparent
          focus:border-blue-600 focus:outline-none
          xl:text-2xl mt-4
        "
      />
      <label
        htmlFor={label}
        className="
          absolute left-0 top-[-3px]
          text-xs sm:text-sm md:text-base
          text-gray-500 transition-all
          peer-placeholder-shown:top-2.5
          peer-placeholder-shown:text-sm sm:peer-placeholder-shown:text-base
          peer-placeholder-shown:text-gray-400
          peer-focus:text-blue-600
          xl:text-2xl
        "
      >
        {label}
      </label>
    </div>
  );
}

export default Email;
