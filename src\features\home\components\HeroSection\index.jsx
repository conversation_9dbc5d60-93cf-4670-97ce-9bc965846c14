const heroImage = "/images/heroImage.webp";

const HeroSection = () => {
  return (
    <section className="relative w-full h-[clamp(40rem,100vh,120rem)] min-h-[40rem] overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0 w-full h-full">
        <img
          src={heroImage}
          alt="LuxInvest Hero Banner"
          className="w-full h-full object-cover object-center"
        />
      </div>

      {/* Top shadow */}
      <div className="absolute top-0 left-0 w-full h-[46vh] bg-gradient-to-b from-white/42 to-transparent z-10"></div>

      {/* Bottom shadow */}
      <div className="absolute top-[43.5vh] left-0 w-full h-[57.3vh] bg-gradient-to-b from-transparent via-black/68 to-black/68 z-10"></div>

      {/* Hero Content */}
      <div className="absolute bottom-0 left-0 w-full z-20 flex flex-col justify-end items-start px-5 sm:px-8 md:px-12 lg:px-16 pb-14 sm:pb-20 md:pb-24">
<h1 className="font-playfair  text-white text-left mb-4 sm:mb-6 lg:leading-[68px ">
  <span
    className="block  font-bold
    text-[2rem] sm:text-[3rem] md:text-[4rem] lg:text-[85.33px]
    leading-[1.2] md:leading-[1.2] ]"
  >
    Trusted Investment
  </span>
  <span
    className="block 
    text-[1.8rem] sm:text-[2.5rem] md:text-[3.5rem] lg:text-[75.49px]
    leading-[1.2] md:leading-[1.2] lg:leading-[68px]"
  >
    Partner in UAE's Property Market
  </span>
</h1>


<p
  className="text-white font-outfit text-left
  text-[1rem] sm:text-[1.25rem] md:text-[1.5rem] lg:text-[24px]
  leading-[1.4] md:leading-[1.4] lg:leading-[120%]
  max-w-[92%] sm:max-w-[80%] md:max-w-[60%]"
>
  12+ Years of High-Value Real Estate Expertise
</p>

      </div>
    </section>
  );
};

export default HeroSection;
