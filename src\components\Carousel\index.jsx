import { useState, useEffect } from "react";
import CarouselController from "./components/CarouselController";
import Card from "./../Card/Card";
import constructUrl from "../../utils/constructUrl";
import { useNavigate } from "react-router-dom";

const Carousel = ({
  carouselData,
  itemsPerIndex = 3,
  isDark = false,
  ApiImg = false,
}) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [screenType, setScreenType] = useState("desktop"); // "mobile" | "tablet" | "desktop"
  const navigate = useNavigate();

  // Detect screen size
  useEffect(() => {
    const checkScreen = () => {
      const w = window.innerWidth;

      if (w < 920) setScreenType("mobile");
      else if (w >= 920 && w < 1180) setScreenType("tablet");
      else setScreenType("desktop");
    };

    checkScreen();
    window.addEventListener("resize", checkScreen);
    return () => window.removeEventListener("resize", checkScreen);
  }, []);

  // Items per index based on screen
  const currentItemsPerIndex =
    screenType === "mobile" ? 1 : screenType === "tablet" ? 2 : itemsPerIndex;

  const getImgSrc = (item) =>
    ApiImg ? constructUrl(item.mainImage) : item.image;

  const renderCard = (item) => (
    <div
      key={item.id}
      className="
      
      group justify-self-center relative cursor-pointer hover:scale-101 duration-300"
      onClick={() => ApiImg && navigate(`/resale-Property/${item._id}`)}
      style={{ maxWidth: "551px" }}
    >
      <Card
        imgSrc={getImgSrc(item)}
        title={item.title}
        destination={item.destination}
        subtitle={item.description}
        isDark={isDark}
      />
      {/* {ApiImg && (
        <div
          className="absolute inset-0 flex items-center justify-center text-white text-2xl font-semibold opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          style={{ backgroundColor: "rgba(0, 0, 0, 0.3)" }}
        >
          Show Details
        </div>
      )} */}
    </div>
  );

  // Slice visible items
  const visibleItems = carouselData.filter(
    (_, index) =>
      index >= currentItemsPerIndex * activeIndex &&
      index <= currentItemsPerIndex * activeIndex + (currentItemsPerIndex - 1)
  );

  return (
    <div className="w-full mx-auto">
      {/* Carousel */}
      <div
        className={`grid gap-6 mb-6 ${
          screenType === "mobile"
            ? "grid-cols-1 px-4"
            : screenType === "tablet"
            ? "grid-cols-2"
            : "grid-cols-3"
        }`}
      >
        {visibleItems.map(renderCard)}
      </div>

      {/* Controller */}
      <CarouselController
        totalItems={carouselData.length}
        activeIndex={activeIndex}
        onIndexChange={setActiveIndex}
        itemsPerIndex={currentItemsPerIndex}
      />
    </div>
  );
};

export default Carousel;
