import { useEffect, useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import {
  FiMapPin,
  FiFolderPlus,
  FiHome,
  FiArrowRight,
  FiStar,
  FiSearch,
  FiFilter,
} from "react-icons/fi";
import useProjectStore from "../../store/destinationStore";
import useResaleStore from "../../store/resaleStore";
import constructUrl from "../../../../utils/constructUrl";

const ProjectsGrid = () => {
  const {
    projects,
    loading: projectLoading,
    fetchProjects,
    filters: projectFilters,
    setFilters: setProjectFilters,
  } = useProjectStore();

  const {
    resales,
    loading: resaleLoading,
    fetchResales,
    filters: resaleFilters,
    setFilters: setResaleFilters,
  } = useResaleStore();

    const navigate = useNavigate();

  const params = new URLSearchParams(window.location.search);
  const currentType = params.get("type") || "resale";

  const isOffPlan = currentType === "off-plan";
  const currentProjects = isOffPlan ? projects : resales;
  const loading = isOffPlan ? projectLoading : resaleLoading;
  const currentFilters = isOffPlan ? projectFilters : resaleFilters;

  // Track per-image loaded state
  const [imagesLoaded, setImagesLoaded] = useState({});

  // ✅ Only reset when the *set of IDs* changes, not on every store update
  const idsSignature = (currentProjects || []).map((p) => p._id).join(",");
  useEffect(() => {
    setImagesLoaded({});
  }, [idsSignature]);

  useEffect(() => {
    if (isOffPlan) {
      fetchProjects(projectFilters);
    } else {
      fetchResales(resaleFilters);
    }
  }, [isOffPlan, projectFilters, resaleFilters, fetchProjects, fetchResales]);

  const handleImageLoad = (id) => {
    setImagesLoaded((prev) => ({ ...prev, [id]: true }));
  };

  // 🔹 Skeleton while fetching list
  if (loading) {
    return (
      <section className="px-4 sm:px-8 lg:px-20 py-10 bg-gray-50">
        <h2 className="text-4xl font-extrabold mb-10 text-gray-800 text-center tracking-tight">
          Available <span className="text-primary decoration-2">Projects</span>
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-10">
          {Array.from({ length: 6 }).map((_, idx) => (
            <div
              key={idx}
              className="bg-white shadow-xl overflow-hidden flex flex-col min-h-[420px] border border-gray-100 animate-pulse"
            >
              {/* Image skeleton (same height as real image) */}
              <div className="relative h-72 w-full bg-gray-300" />
              {/* Content skeleton */}
              <div className="p-6 flex-1 flex flex-col justify-between space-y-4">
                <div className="space-y-4">
                  <div className="h-6 bg-gray-300 rounded w-3/4" />
                  <div className="h-5 bg-gray-300 rounded w-1/2" />
                  <div className="h-4 bg-gray-300 rounded w-full" />
                  <div className="h-4 bg-gray-300 rounded w-5/6" />
                </div>
                <div className="w-full h-12 bg-gray-300 rounded-xl" />
              </div>
            </div>
          ))}
        </div>
      </section>
    );
  }

  // Check if filters are applied
  const hasActiveFilters =
    currentFilters &&
    ((isOffPlan &&
      ((currentFilters.destination && currentFilters.destination !== "all") ||
        (currentFilters.developer && currentFilters.developer !== "all"))) ||
      (!isOffPlan &&
        (currentFilters.destination ||
          currentFilters.area ||
          currentFilters.isFeatured !== null ||
          currentFilters.minPrice !== null ||
          currentFilters.maxPrice !== null)));

  if (!currentProjects || (currentProjects.length === 0 && !hasActiveFilters)) {
    return (
      <section className="flex flex-col items-center justify-center text-center py-20 px-4">
        <FiFolderPlus className="w-16 h-16 text-gray-400 mb-4" />
        <h2 className="text-2xl font-semibold text-gray-800 mb-2">
          No Projects Yet
        </h2>
        <p className="text-gray-500 max-w-md">
          There are no projects avaliable yet. Once there are, they'll show up
          here.
        </p>
      </section>
    );
  }

  if (hasActiveFilters && currentProjects.length === 0) {
    return (
      <section className="px-4 sm:px-8 lg:px-20 py-10 bg-gray-50">
        <h2 className="text-4xl font-extrabold mb-10 text-gray-800 text-center tracking-tight">
          Available{" "}
          <span className="text-primary italic underline decoration-2">
            Projects
          </span>
        </h2>
        <div className="flex flex-col items-center justify-center py-20">
          <div className="bg-white rounded-3xl shadow-xl p-12 max-w-2xl text-center">
            <div className="flex justify-center mb-6">
              <div className="bg-primary/10 p-6 rounded-full">
                <FiSearch className="text-6xl text-primary" />
              </div>
            </div>
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              No Projects Found
            </h3>
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              We couldn't find any projects matching your current filters. Try
              adjusting your search criteria or browse all available projects.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => {
                  const resetFilters = {
                    destination: null,
                    area: null,
                    isFeatured: null,
                    minPrice: null,
                    maxPrice: null,
                  };
                  setResaleFilters(resetFilters);
                  fetchResales(resetFilters);
                  window.location.search = "?type=resale";
                }}
                className="flex items-center justify-center gap-3 bg-primary text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-primary/90 transition-colors"
              >
                <FiFilter className="text-xl" />
                Clear All Filters
              </button>
              <Link
                to="/destinations"
                className="flex items-center justify-center gap-3 border-2 border-primary text-primary px-8 py-4 rounded-xl font-semibold text-lg hover:bg-primary hover:text-white transition-colors"
              >
                <FiArrowRight className="text-xl" />
                Browse All Projects
              </Link>
            </div>
          </div>
        </div>
      </section>
    );
  }

return (
    <section className="px-4 sm:px-8 lg:px-20 xl:px-30 bg-gray-50">
      <div className="mx-auto max-w-[1762px] py-10">
        <h2 className="text-4xl font-extrabold mb-10 text-gray-800 text-center tracking-tight">
          Available <span className="text-primary decoration-2">Projects</span>
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-10 justify-center">
          {currentProjects.map(project => {
            const projectPath =
              currentType === 'resale'
                ? `/resale-Property/${project._id}`
                : `/offPlan-Property/${project._id}`;

            return (
              <div
                key={project._id}
                className="flex flex-col items-center w-full"
              >
                {/* IMAGE CARD (clickable & skeleton) */}
                <div
                  onClick={() => navigate(projectPath)}
                  className="
                    relative
                    w-full
                    aspect-[541/591]
                    lg:aspect-auto
                    lg:h-[591px]
                    [@media(max-width:1855px)]:w-[400px]
                    [@media(max-width:1855px)]:h-[450px]
                    [@media(max-width:1420px)]:!w-[350px]
                    [@media(max-width:1420px)]:!h-[400px]
                    overflow-hidden
                    cursor-pointer
                  "
                >
                  {/* skeleton placeholder */}
                  {!imagesLoaded[project._id] && (
                    <div className="absolute inset-0 bg-gray-300 animate-pulse" />
                  )}

                  <img
                    src={constructUrl(project.mainImage)}
                    alt={project.title}
                    loading="lazy"
                    onLoad={() => handleImageLoad(project._id)}
                    className={`w-full h-full object-cover object-center transition-opacity duration-500 ${
                      imagesLoaded[project._id] ? 'opacity-100' : 'opacity-0'
                    }`}
                  />

                  {/* Badge */}
                  <div className="absolute top-4 left-4 flex flex-col gap-2 z-10">
                    <span className="inline-flex items-center gap-2 bg-primary text-white px-3 py-1.5 rounded-full text-sm font-semibold shadow-md">
                      {project.type.toUpperCase()}
                    </span>
                  </div>
                </div>

                {/* TITLE & DESCRIPTION */}
                <div className="mt-6 w-full text-center">
                  <h3 className="text-2xl font-extrabold text-gray-900 leading-tight mb-1">
                    {project.title}
                  </h3>
                  <div className="text-lg font-medium text-gray-700 mb-2">
                    {project.location}
                  </div>
                  <p className="text-gray-700 text-base leading-relaxed line-clamp-3">
                    {project.desc}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default ProjectsGrid;
