import { useEffect, useState } from "react";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import Controller from "./Controller";
import useCustomerReviewsStore from "../../../store/customerReviewsStore";
import starIco from "/icons/star.svg";
import constructUrl from "../../../utils/constructUrl";

function CustomerReviews() {
  const { customerReviews, fetchCustomerReviews, loading } =
    useCustomerReviewsStore();

  const [activeIndex, setActiveIndex] = useState(0);
  const [imagesLoaded, setImagesLoaded] = useState({});

  useEffect(() => {
    fetchCustomerReviews(1, 10);
  }, [fetchCustomerReviews]);

  const handleImageLoad = (id) => {
    setImagesLoaded((prev) => ({ ...prev, [id]: true }));
  };

  const onReviewChange = (isForward) => {
    setActiveIndex((prev) => {
      if (customerReviews.length === 0) return 0;
      if (isForward) return (prev + 1) % customerReviews.length;
      return (prev - 1 + customerReviews.length) % customerReviews.length;
    });
  };

  if (loading || customerReviews.length === 0) {
    return (
      <section className="pb-20 pt-14 bg-secondary text-white">
        <div className="container mx-auto px-4 sm:px-6 md:px-12">
          <div className="flex flex-col md:flex-row justify-between gap-8 md:gap-20">
            <div className="flex flex-col justify-around flex-1 gap-6 md:gap-8">
              <div>
                <h2 className="text-3xl sm:text-4xl md:text-5xl mb-4">
                  What Our{" "}
                  <span className="text-primary italic font-semibold">
                    Customers
                  </span>{" "}
                  Say
                </h2>
                <p className="font-sans text-sm sm:text-base md:text-lg leading-tight">
                  Following are some responses from our customers who are
                  satisfied with the service from our home
                </p>
              </div>
              <Skeleton height={20} width={120} />
            </div>

            <div className="z-10 py-12 sm:py-20 min-h-[50vh] md:min-h-[70vh] px-4 sm:px-8 md:px-20 flex-1 md:flex-[2]">
              <Skeleton height="100%" width="100%" className="rounded-lg" />
            </div>
          </div>
        </div>
      </section>
    );
  }

  const activeReview = customerReviews[activeIndex];

  return (
    <section className="pb-20 pt-14 bg-secondary text-white">
      <div className="container container-smaller mx-auto px-4 sm:px-6 md:px-12">
        <div className="flex items-center flex-col lg:flex-row justify-between gap-8 md:gap-20">
          {/* Text + Controller */}
          <div className="flex flex-col justify-around flex-1 gap-6 md:gap-8">
            <div className="xl:mb-30">
              <h2 className="text-3xl sm:text-4xl md:text-5xl mb-4">
                What Our{" "}
                <span className="text-primary italic font-semibold">
                  Customers
                </span>{" "}
                Say
              </h2>
              <p className="font-sans text-sm sm:text-base md:text-lg leading-tight">
                Following are some responses from our customers who are
                satisfied with the service from our home
              </p>
            </div>

            <Controller
              onReviewChange={onReviewChange}
              totalItems={customerReviews.length}
              activeItemIndex={activeIndex}
            />
          </div>

          {/* Image + Review */}
          <div
            style={{ aspectRatio: "960/692" }}
            className="relative z-10 flex flex-col justify-end gap-4 sm:gap-6 md:gap-8 flex-1 md:flex-[2] overflow-hidden rounded-lg font-sans"
          >
            {!imagesLoaded[activeReview._id] && (
              <div className="absolute inset-0 w-full h-full animate-pulse bg-gray-700 rounded-lg">
                <Skeleton
                  height="100%"
                  width="100%"
                  baseColor="#374151"
                  highlightColor="#4B5563"
                  className="rounded-lg"
                />
              </div>
            )}
            <img
              src={constructUrl(activeReview.img)}
              alt={activeReview.authorName}
              className={`absolute inset-0 w-full h-full object-cover transition-opacity duration-700 rounded-lg ${
                imagesLoaded[activeReview._id] ? "opacity-100" : "opacity-0"
              }`}
              onLoad={() => handleImageLoad(activeReview._id)}
            />

            <div className="py-10 px-20 flex flex-col gap-8">
              {/* Stars */}
              <div className="relative flex gap-1 z-10">
                {Array.from({ length: activeReview.stars }).map((_, index) => (
                  <img
                    key={index}
                    src={starIco}
                    alt="star"
                    className="w-4 h-4 sm:w-5 sm:h-5"
                  />
                ))}
              </div>
              {/* Comment */}
              <p className="relative z-10 text-xl sm:text-2xl md:text-3xl">
                “{activeReview.quote}”
              </p>
              {/* Author */}
              <div className="relative z-10">
                <p className="text-base sm:text-lg md:text-xl">
                  {activeReview.authorName}
                </p>
                <p className="text-xs sm:text-sm md:text-base text-gray">
                  {activeReview.role}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default CustomerReviews;
