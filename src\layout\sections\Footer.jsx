import { Link } from "react-router-dom";

function FooterSection() {
  return (
    <footer className="bg-secondary text-white py-10">
      <div className="container container-smaller mx-auto flex justify-between flex-wrap gap-5">
        {/* Responsive headline */}
        <p
          className="
            text-2xl sm:text-3xl md:text-5xl
            leading-snug sm:leading-tight md:leading-[3.5rem]
            m-auto md:m-0
          "
        >
          Together, <br />
          <span className="text-primary">
            <span className="italic">let’s</span> achieve{" "}
            <span className="italic">excellence</span>
          </span>
        </p>

        <div className="flex flex-col justify-around gap-8 m-auto md:m-0">
          {/* Navigation with responsive fonts */}
          <nav>
            <ul className="flex gap-2 sm:gap-6 md:gap-8 text-xs sm:text-lg md:text-xl">
              <Link to="/">
                <li className="font-sans">
                  <a className="cursor-pointer">Home</a>
                </li>
              </Link>
              <Link to="/about">
                <li className="font-sans">
                  <a className="cursor-pointer">About us</a>
                </li>
              </Link>
              <Link to="/services">
                <li className="font-sans">
                  <a className="cursor-pointer">Our services</a>
                </li>
              </Link>
              <Link to="/destinations">
                <li className="font-sans">
                  <a className="cursor-pointer">Destinations</a>
                </li>
              </Link>
              <Link to="/news">
                <li className="font-sans">
                  <a className="cursor-pointer">News</a>
                </li>
              </Link>
            </ul>
          </nav>

          {/* Footer bottom text with scaling */}
          <div className="flex justify-end text-xs sm:text-sm md:text-base text-gray">
            <a href="#top" className="mr-4 sm:mr-6 cursor-pointer">
              Back to top ↑
            </a>
            <span>©Luxinvest2025 all rights reserved</span>
          </div>
        </div>
      </div>
    </footer>
  );
}

export default FooterSection;
