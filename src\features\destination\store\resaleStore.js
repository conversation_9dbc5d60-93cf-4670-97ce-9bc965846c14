// store/resaleStore.js
import { create } from "zustand";

const useResaleStore = create((set) => ({
  resales: [],
  filters: {
    area: null,
    destination: null,
    isFeatured: null,
    minPrice: null,
    maxPrice: null,
  },
  loading: false,
  error: null,

  setFilters: (filters) => set({ filters }),

  fetchResales: async (filters = {}, pageSize = 100, pageNumber = 1) => {
    set({
      loading: true,
      error: null,
      filters,
    });

    try {
      const filterConstructor = {};

      // AREA
      if (filters.area && filters.area !== "all") {
        filterConstructor.area = filters.area;
      }

      // DESTINATION
      if (filters.destination && filters.destination !== "all") {
        filterConstructor.destination = filters.destination;
      }

      // IS FEATURED
      if (filters.isFeatured !== null && filters.isFeatured !== undefined) {
        filterConstructor.isFeatured = filters.isFeatured;
      }

      // PRICE RANGE
      if (filters.minPrice !== null && filters.minPrice !== undefined) {
        filterConstructor.minPrice = filters.minPrice;
      }

      if (filters.maxPrice !== null && filters.maxPrice !== undefined) {
        filterConstructor.maxPrice = filters.maxPrice;
      }

      let url = `${import.meta.env.VITE_API_BASE_URL}/api/resales/get?pageSize=${pageSize}&pageNumber=${pageNumber}`;

      console.debug("fetchResales: POST body:", filterConstructor);

      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(filterConstructor),
      });

      if (!response.ok) {
        throw new Error(`Server error: ${response.status}`);
      }

      const data = await response.json();

      set({
        resales: data.resales || data.properties || [],
        loading: false,
      });
    } catch (error) {
      console.error("❌ Resale API Error:", error);
      set({
        error: error.message,
        loading: false,
      });
    }
  },
}));

export default useResaleStore;
