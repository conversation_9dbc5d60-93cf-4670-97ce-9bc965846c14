import { MdArrowForward } from "react-icons/md";
import { BsArrowUpRight } from "react-icons/bs";
import useNewsStore from "../../../store/newsStore";
import { useEffect, useMemo, useState } from "react";
import constructUrl from "../../../utils/constructUrl";
import { useNavigate } from "react-router-dom";
export default function NewsList() {
  const { news, newsLoading, fetchNews } = useNewsStore();
  const [imgLoaded, setImgLoaded] = useState(false);

const navigate = useNavigate();

  useEffect(() => {
    fetchNews();
  }, [fetchNews]);

  // Sort latest first
  const sortedNews = useMemo(() => {
    if (!news || news.length === 0) return [];
    return [...news].sort(
      (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
    );
  }, [news]);

  const latestNews = sortedNews[0];
  const otherNews = sortedNews.slice(1);

  /** ------------------
   * Loading State UI
   * ------------------ */
  if (newsLoading) {
    return (
      <div className="flex flex-col md:flex-row bg-secondary pt-55 gap-8 p-6 animate-pulse">
        {/* Left skeleton */}
        <div className="flex flex-col w-full md:w-2/3 lg:w-[54rem] gap-4">
          <div className="bg-gray-500/30 rounded w-full aspect-[861/550]" />
          <div className="h-8 bg-gray-500/30 rounded w-3/4" />
          <div className="h-4 bg-gray-500/30 rounded w-1/2" />
        </div>
        {/* Right skeleton list */}
        <div className="w-full md:w-1/2 space-y-6">
          {Array.from({ length: 3 }).map((_, idx) => (
            <div key={idx} className="space-y-2 border-b border-b-white pb-4">
              <div className="h-3 w-1/4 bg-gray-500/30 rounded" />
              <div className="h-6 w-3/4 bg-gray-500/30 rounded" />
              <div className="h-3 w-1/3 bg-gray-500/30 rounded" />
            </div>
          ))}
        </div>
      </div>
    );
  }

if (!latestNews  && (!latestNews || latestNews.length === 0)) {
  return (
    <div className="flex flex-col items-center justify-center min-h-[70vh] bg-secondary text-white px-6 py-20">
      {/* Icon */}
      <div className="bg-[#bea15d]/20 rounded-full p-6 mb-6">
        <MdArrowForward className="text-[#BEA15D] text-4xl" />
      </div>

      {/* Heading */}
      <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white text-center">
        No News Available
      </h2>

      {/* Description */}
      <p className="text-lg md:text-xl text-gray-200 text-center max-w-lg mb-6">
        We currently don’t have any news to show. Please check back later or explore other sections of our site.
      </p>

      {/* Optional CTA */}
      <button
        onClick={fetchNews}
        className="flex items-center gap-2 px-6 py-3 rounded-lg bg-[#BEA15D] text-white font-semibold hover:bg-[#a88d50] transition-shadow shadow-md"
      >
        Refresh
        <BsArrowUpRight className="text-white text-lg" />
      </button>
    </div>
  );
}
  return (
    <div className="flex flex-col md:flex-row bg-secondary pt-55 items-center md:items-start justify-between gap-8">
      
      {/* Left section - Latest News */}
      <div className="flex flex-col flex-shrink-0 w-full md:w-2/3 lg:w-[60rem]">
        <div className="relative aspect-[861/550]">
          {!imgLoaded && (
            <div className="absolute inset-0 bg-gray-500/30 animate-pulse rounded" />
          )}
          <img
            src={constructUrl(latestNews.img)}
            alt={latestNews.title}
            className={`w-full h-full object-cover rounded transition-opacity duration-500 ${imgLoaded ? "opacity-100" : "opacity-0"}`}
            loading="lazy"
            onLoad={() => setImgLoaded(true)}
          />
        </div>

        <div className="mt-4">
              <p className="font-[Outfit] font-light text-sm leading-loose tracking-tight text-[#BEA15D] inline-block px-2">
              {new Date(latestNews.updatedAt).toLocaleDateString()}
            </p>
          <h3 className="font-[Playfair_Display] font-medium text-3xl  tracking-tight text-white flex gap-5">
            {latestNews.title}
            <MdArrowForward className="text-white text-2xl inline-block" />
          </h3>
          <button  onClick={() => navigate(`/news/${latestNews._id}`)}
 className="font-[Outfit] font-light text-sm md:text-lg  leading-loose tracking-tight text-white mt-2 hover:underline flex gap-2">
            Read More
            <BsArrowUpRight className="text-white text-sm" />
          </button>
        </div>
      </div>

      {/* Right section - Other news */}
      <div className="w-full md:w-1/2 p-6 space-y-6 overflow-y-auto max-h-[861px]">
        {otherNews.map((item) => (
          <div key={item._id} className="border-b border-b-white pb-4">
            <p className="font-[Outfit] font-light text-sm leading-loose tracking-tight text-[#BEA15D] inline-block px-2">
              {new Date(item.updatedAt).toLocaleDateString()}
            </p>
            <h2 className="font-[Playfair_Display] font-medium text-2xl sm:text-3xl leading-loose tracking-tight text-white mt-2">
              {item.title}
            </h2>
            <button  onClick={() => navigate(`/news/${item._id}`)}
 className="font-[Outfit] font-light text-sm md:text-lg leading-loose tracking-tight text-white mt-2 hover:underline flex gap-2">
              Read More
              <BsArrowUpRight className="text-white text-sm" />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
}