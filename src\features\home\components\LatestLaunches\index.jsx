import { useEffect, useState } from "react";
import Subtitle from "../../../../components/Subtitle";
import useProjectStore from "../../../destination/store/destinationStore";
import constructUrl from "../../../../utils/constructUrl";
import { useNavigate } from "react-router-dom";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import useDeveloperStore from "../../../destination/store/developerStore";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";

function LatestLaunches() {
  const { projects, fetchProjects, loading } = useProjectStore();
  const { developers, fetchDevelopers } = useDeveloperStore();
  const [imgLoadError, setImgLoadError] = useState({});
  const [imgLoaded, setImgLoaded] = useState({});
  const navigate = useNavigate();
  const [screenWidth, setScreenWidth] = useState(window.innerWidth);

  const imageSizes = [
    { width: 278, height: 244 },
    { width: 580, height: 600, text: true },
    { width: 391, height: 244 },
    { width: 584, height: 352 },
  ];

  useEffect(() => {
    const handleResize = () => {
      setScreenWidth(window.innerWidth);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    fetchProjects({}, 10);
    fetchDevelopers();
  }, [fetchProjects, fetchDevelopers]);

  const handleClick = (id) => {
    navigate(`/offPlan-Property/${id}`);
  };

  const handleImgError = (id) => {
    setImgLoadError((prev) => ({ ...prev, [id]: true }));
  };

  const handleImgLoad = (id) => {
    setImgLoaded((prev) => ({ ...prev, [id]: true }));
  };

  const latestProjects = [...projects].slice(-10).reverse();

  const sliderSettings = {
    infinite: true,
    speed: 500,
    slidesToShow: 3,
    slidesToScroll: 1,
    autoplay: true,
    variableWidth: true,
    pauseOnHover: false,
    autoplaySpeed: 3000,
    cssEase: "ease",
    arrows: false,
    swipeToSlide: true,
    draggable: true,
    accessibility: true,
    responsive: [
      { breakpoint: 1024, settings: { slidesToShow: 3 } },
      { breakpoint: 640, settings: { slidesToShow: 1 } },
    ],
  };

  return (
    <section className="w-full mx-auto bg-secondary text-white py-14 sm:py-20 lg:px-0 overflow-hidden">
      <div className="mx-auto px-4 sm:px-6 md:px-50">
        {/* Responsive headline */}
        <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl text-center mx-auto font-medium leading-snug sm:leading-tight md:leading-[3.2rem] mb-6 sm:mb-10">
          Discover a{" "}
          <span className="text-primary italic font-semibold">
            Premium Selection
          </span>
          <br />
          of our properties and Exclusive Deals
        </h2>
        <Subtitle text="Latest Launches" />
      </div>

      {/* Carousel */}
      <div className="mt-10 sm:mt-12 min-h-[20rem]">
        {loading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            {Array(3)
              .fill(0)
              .map((_, i) => (
                <div key={i} className="rounded-xl overflow-hidden shadow-lg">
                  <div className="animate-pulse w-full">
                    <div className="w-full h-48 sm:h-60 lg:h-80">
                      <Skeleton
                        height="100%"
                        width="100%"
                        className="rounded-t-xl"
                      />
                    </div>
                    <div className="w-full h-8 sm:h-10 lg:h-12 mt-2">
                      <Skeleton
                        height="100%"
                        width="100%"
                        className="rounded-b-xl"
                      />
                    </div>
                  </div>
                </div>
              ))}
          </div>
        ) : latestProjects.length === 0 ? (
          <p className="text-center text-base sm:text-lg">
            No projects available at the moment.
          </p>
        ) : (
          <Slider {...sliderSettings} aria-label="Latest projects carousel">
            {latestProjects.map((project, index) => {
              const hasError = imgLoadError[project._id];
              const isLoaded = imgLoaded[project._id];
              const { width, height, text } =
                imageSizes[index % imageSizes.length];

              // Conditional height calculation for small screens
              const calculatedWidth =
                screenWidth <= 640 ? Math.min(width, 320) : width;

              return (
                <div
                  key={project._id}
                  className="px-2 sm:px-3 cursor-pointer outline-none"
                  role="button"
                  tabIndex={0}
                  aria-describedby={`desc-${project._id}`}
                  onClick={() => handleClick(project._id)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" || e.key === " ") {
                      handleClick(project._id);
                    }
                  }}
                >
                  <div className="group overflow-hidden transform transition-transform duration-300 ease-in-out hover:scale-105">
                    {/* ✅ Skeleton until image loads */}
                    {!isLoaded && !hasError && (
                      <div className="animate-pulse">
                        <div
                          className={`relative w-[${width}px] h-[${height}px] overflow-hidden`}
                        >
                          <Skeleton height={height} width={width} />
                        </div>
                      </div>
                    )}

                    {!hasError ? (
                      <div className="relative">
                        <img
                          src={constructUrl(project.mainImage)}
                          alt={project.title}
                          className={`object-cover  transition-opacity duration-500 ${
                            isLoaded ? "opacity-100" : "opacity-0 absolute"
                          }`}
                          style={{
                            maxWidth: `${calculatedWidth}px`,
                            maxHeight: `${height}px`,
                            aspectRatio: `${width}/${height}`,
                          }}
                          loading="lazy"
                          onLoad={() => handleImgLoad(project._id)}
                          onError={() => handleImgError(project._id)}
                        />
                        {/* Hover overlay */}
                        <div
                          className="absolute inset-0 flex items-center justify-center text-white text-lg sm:text-xl md:text-2xl font-semibold opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none select-none"
                          style={{ backgroundColor: "rgba(0, 0, 0, 0.5)" }}
                        >
                          See Details
                        </div>
                      </div>
                    ) : (
                      <div
                        className="bg-gray-700 flex items-center justify-center w-full "
                        style={{ height: `${height}px` }}
                      >
                        <p className="text-gray-400 italic text-center px-3">
                          Image not available
                        </p>
                      </div>
                    )}

                    {/* Title */}
                    {text && (
                      <div
                        className="relative  overflow-hidden"
                        title={project.title}
                        id={`desc-${project._id}`}
                      >
                        <h3 className="relative z-10 text-white font-normal text-3xl py-3 line-clamp-2 break-words">
                          {project.title}
                        </h3>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </Slider>
        )}
      </div>

      {/* Companies Row */}
      <div className="mt-20 w-full flex flex-col  lg:flex-row gap-8  items-center text-gray-400 gap-y-4 lg:justify-between">
        <p className="whitespace-nowrap  text-center lg:text-left mx-auto lg:mx-0 text-sm md:text-2xl md:pl-55 ">
          Trusted by
          <span className="pl-1 sm:pl-2 font-semibold text-white">
            150+ companies
          </span>
          <br className="hidden sm:block" />
          from startups to enterprises:
        </p>
        <ScrollingBanner
          companies={developers.map((dev) => dev.name.toUpperCase())}
          speed={15}
        />
      </div>
    </section>
  );
}

export default LatestLaunches;

const ScrollingBanner = ({ companies, speed = 30 }) => {
  const shouldScroll = companies.length > 4;

  return (
    <div className="banner-container font-sans">
      <div
        className="banner-content"
        style={{
          "--duration": `${speed}s`,
          animation: shouldScroll ? undefined : "none",
        }}
      >
        <div className="banner-items">
          {companies.map((company, index) => (
            <span key={`original-${index}`} className="banner-item">
              {company}
            </span>
          ))}
        </div>

        {shouldScroll && (
          <div className="banner-items" aria-hidden="true">
            {companies.map((company, index) => (
              <span key={`duplicate-${index}`} className="banner-item">
                {company}
              </span>
            ))}
          </div>
        )}
      </div>

      <style jsx>{`
        .banner-container {
          width: 100%;
          overflow: hidden;
          padding: 1.5rem 0;
        }
        .banner-content {
          display: flex;
          animation: scroll var(--duration, 20s) linear infinite;
          width: max-content;
        }
        .banner-items {
          display: flex;
          margin-right: 2rem;
          gap: 2rem;
          white-space: nowrap;
        }
        .banner-item {
          letter-spacing: 0.15rem;
          color: white;
          font-weight: 600;
          font-size: 0.875rem;
          flex-shrink: 0;
        }
        @media (min-width: 640px) {
          .banner-item {
            font-size: 1rem;
          }
        }
        @media (min-width: 1024px) {
          .banner-item {
            font-size: 1.5rem;
          }
        }
        @keyframes scroll {
          0% {
            transform: translateX(0);
          }
          100% {
            transform: translateX(-50%);
          }
        }
        .banner-container:hover .banner-content {
          animation-play-state: paused;
        }

        /* Custom style to vertically center slider items */
        .slick-track {
          display: flex;
          align-items: center;
        }
      `}</style>
    </div>
  );
};
