import { useEffect, useState } from "react";
import Carousel from "../../../../components/Carousel";
import useCuratedResaleStore from "../../../destination/store/curatedResaleStore";
import Subtitle from "./../../../../components/Subtitle/index";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";

const categories = [
  { label: "DUBAI", key: "689b11998a0bd10eddf9c663" },
  { label: "ABU-DHABI", key: "689b11b48a0bd10eddf9c66d" },
  { label: "ALL PROPERTIES", key: "all" },
];

function CuratedSecondaryProperties() {
  const { fetchResales, resales, loading } = useCuratedResaleStore();
  const [selectedCategory, setSelectedCategory] = useState("all");

  useEffect(() => {
    fetchResales({ isFeatured: true, destination: selectedCategory });
  }, [fetchResales, selectedCategory]);

  if (!loading && resales.length === 0)
    return (
      <section className="py-20 bg-[#EBE6E2] text-secondary">
        <div className="mx-auto px-4 sm:px-6 md:px-12">
          <Subtitle text="Our hot deals" />
          <div className="flex flex-col gap-6">
            <h2 className="text-[#12253F] text-3xl sm:text-4xl md:text-7xl font-medium">
              Curated Secondary Properties
            </h2>
            <p className="text-gray-500 text-base sm:text-lg md:text-xl">
              Right Now No Resale Properties Available
            </p>
          </div>
        </div>
      </section>
    );

  return (
    <section className="py-20 bg-[#EBE6E2] text-secondary">
      <div className="container mx-auto px-4 sm:px-6 md:px-12">
        <Subtitle text="Our hot deals" />
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6 md:gap-12 flex-wrap">
          <h2 className="text-[#12253F] text-3xl sm:text-4xl md:text-7xl font-medium">
            Curated Secondary Properties
          </h2>

          <div className="flex flex-wrap gap-4 sm:gap-6 md:gap-8 justify-start md:justify-end ml-auto">
            {categories.map((category) => (
              <button
                key={category.key}
                onClick={() => setSelectedCategory(category.key)}
                className={`text-base sm:text-lg md:text-2xl whitespace-nowrap text-[#93918BB2] font-normal font-outfit ${
                  selectedCategory === category.key
                    ? "underline text-primary underline-offset-4 sm:underline-offset-6 decoration-2 decoration-primary"
                    : ""
                }`}
              >
                {category.label}
              </button>
            ))}
          </div>
        </div>

        <div className="mt-6 md:mt-10">
          {loading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 sm:gap-6 md:gap-8">
              {Array(3)
                .fill(0)
                .map((_, i) => (
                  <div
                    key={i}
                    className="rounded-xl overflow-hidden shadow-lg animate-pulse"
                  >
                    <div className="w-full h-48 sm:h-60 md:h-72 lg:h-80">
                      <Skeleton
                        height="100%"
                        width="100%"
                        baseColor="#e0e0e0"
                        highlightColor="#c0c0c0"
                        className="rounded-t-xl"
                      />
                    </div>
                    <div className="w-full h-8 sm:h-10 md:h-12 lg:h-14 mt-2">
                      <Skeleton
                        height="100%"
                        width="100%"
                        baseColor="#e0e0e0"
                        highlightColor="#c0c0c0"
                        className="rounded-b-xl"
                      />
                    </div>
                  </div>
                ))}
            </div>
          ) : (
            <Carousel carouselData={resales} ApiImg={true} />
          )}
        </div>
      </div>
    </section>
  );
}

export default CuratedSecondaryProperties;
