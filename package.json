{"name": "luxinvest", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.1.10", "axios": "^1.10.0", "dompurify": "^3.2.6", "quill": "^2.0.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-loading-skeleton": "^3.5.0", "react-router-dom": "^7.6.2", "react-slick": "^0.31.0", "slick-carousel": "^1.8.1", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "tailwindcss": "^4.1.10", "vite": "^6.3.5"}}