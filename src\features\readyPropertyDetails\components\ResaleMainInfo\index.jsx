import { useState } from "react";
import { FiHome, FiStar, FiInfo, FiDollarSign, FiUser, FiMapPin, FiX ,FiBookOpen } from "react-icons/fi";

function ResaleMainInfo({ property }) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  if (!property) return null;

  return (
    <section className="bg-white rounded-2xl shadow-lg p-6 md:p-8 h-full flex flex-col gap-6 relative">
      {/* Title */}
      <div className="flex items-center gap-3 border-b border-gray-100 pb-4">
        <FiHome className="text-3xl md:text-4xl text-primary" />
        <h1 className="text-2xl md:text-4xl font-extrabold text-gray-900">
          {property.title}
        </h1>
      </div>

      {/* Project */}
      {property.project && (
        <div className="flex items-center gap-2 bg-yellow-50 p-4 rounded-xl">
          <FiStar className="text-xl md:text-2xl text-yellow-600" />
          <p className="text-lg md:text-xl font-semibold text-yellow-700 italic">
            {property.project}
          </p>
        </div>
      )}

      {/* Developer */}
      {property.developer && (
        <div className="flex items-center gap-3 bg-gray-50 p-4 rounded-xl">
          <FiUser className="text-xl md:text-2xl text-primary" />
          <p className="text-gray-700 text-base md:text-xl">
            <span className="font-semibold">Developer:</span> {property.developer}
          </p>
        </div>
      )}

      {/* Location */}
      {property.location && (
        <div className="flex items-center gap-3 bg-gray-50 p-4 rounded-xl">
          <FiMapPin className="text-xl md:text-2xl text-primary" />
          <p className="text-gray-700 text-base md:text-xl">
            <span className="font-semibold">Location:</span>{" "}
            {property.location.area?.name}, {property.location.city}
          </p>
        </div>
      )}

     {/* Description Preview with Read More */}
{property.desc && (
  <div className="flex flex-col gap-3 bg-gray-50 p-5 rounded-xl shadow-sm">
    <div className="flex items-start gap-3">
      <FiInfo className="text-xl text-primary mt-1 flex-shrink-0" />
      <p className="text-gray-700 text-base md:text-lg leading-relaxed md:leading-loose whitespace-pre-line">
        {property.desc.length > 650
          ? property.desc.slice(0, 650) + "..."
          : property.desc}
      </p>
    </div>

    {property.desc.length > 650 && (
      <button
        onClick={() => setIsModalOpen(true)}
        className="inline-flex items-center gap-2 justify-center mt-3 px-6 py-2.5 
                   border-2 border-primary text-primary font-bold 
                   text-base md:text-lg rounded-lg 
                   bg-white shadow-md hover:shadow-lg 
                   hover:bg-primary hover:text-white 
                   transition-all duration-200"
      >
        <FiBookOpen className="text-lg" />
        Read More
      </button>
    )}
  </div>
)}
      {/* Price */}
      {property.price && (
        <div className="flex items-center gap-3 bg-primary/10  border border-primary/20 p-5 rounded-2xl shadow-sm">
          
          <div>
            <p className="text-sm md:text-base text-gray-600 font-medium">
              Property Price
            </p>
            <p className="text-2xl md:text-4xl font-extrabold text-primary">
              AED {property.price.toLocaleString()}
            </p>
          </div>
        </div>
      )}

      {/* Tags */}
      <div className="mt-auto pt-4 border-t border-gray-100 flex flex-wrap gap-3">
        {property.type && (
          <span className="inline-flex items-center gap-2 bg-primary text-white px-5 py-2 rounded-full text-sm md:text-lg font-medium">
            <FiHome className="text-sm md:text-base" />
            {property.type}
          </span>
        )}
        {property.destination && (
          <span className="inline-flex items-center gap-2 bg-gray-700 text-white px-5 py-2 rounded-full text-sm md:text-lg font-medium">
            <FiMapPin className="text-sm md:text-base" />
            {property.destination.name}
          </span>
        )}
      </div>
{/* Modal Popup */}
  {isModalOpen && (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="bg-gray-50 w-11/12 md:w-2/3 lg:w-1/2 max-h-[80vh] overflow-hidden rounded-2xl shadow-xl transform transition-all scale-95 animate-scaleIn">
        
        {/* Close Button */}
        <button
          onClick={() => setIsModalOpen(false)}
          className="absolute top-4 right-4 text-gray-600 hover:text-gray-800 text-3xl p-2 rounded-full hover:bg-gray-200 transition"
          aria-label="Close"
        >
          <FiX />
        </button>
        
        {/* Content Area */}
        <div className="p-6 md:p-8 overflow-y-auto max-h-[80vh] space-y-6">
          <h2 className="text-3xl md:text-4xl font-extrabold text-gray-900 mb-4">
            Property Description
          </h2>
          <p className="text-gray-800 text-lg md:text-2xl font-medium leading-relaxed md:leading-loose whitespace-pre-line bg-white p-4 rounded-lg shadow-inner">
            {property.desc}
          </p>
        </div>
      </div>
    </div>
  )}


      {/* Tailwind animation (scale in) */}
      <style jsx>{`
        @keyframes scaleIn {
          0% { transform: scale(0.95); opacity: 0; }
          100% { transform: scale(1); opacity: 1; }
        }
        .animate-scaleIn {
          animation: scaleIn 0.2s ease-out forwards;
        }
      `}</style>
    </section>
  );
}

export default ResaleMainInfo;
