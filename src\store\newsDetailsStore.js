  import { create } from 'zustand';
  
  const useNewsDetailsStore = create((set) => ({
    article: [],
    news: [],
    newsLoading: false,
    articleDetailsLoading: false,
    error: null,
  fetchNewsDetails: async (id) => {
    set({ newsLoading: true, error: null });
    try {
      const res = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/articles/${id}`);
      if (!res.ok) {
        throw new Error('Failed to fetch News');
      }
      const data = await res.json();
      console.log('adde',data);
      set({ news: data || [], newsLoading: false });
    } catch (err) {
      console.error('Failed to fetch News:', err);
      set({ news: [], newsLoading: false, error: err.message });
    }
  },
  fetchArticleDetails: async (id) => {
    set({ articleDetailsLoading: true, error: null });
    try {
      const res = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/articles/${id}`);
      if (!res.ok) {
        throw new Error('Failed to fetch News');
      }
      const data = await res.json();
      console.log(data);
      set({ article: data || [], articleDetailsLoading: false });
    } catch (err) {
      console.error('Failed to fetch News:', err);
      set({ article: [], articleDetailsLoading: false, error: err.message });
    }
  }
  }
  ))
export default useNewsDetailsStore;