.header {
  width: 100%;
  background: transparent;
  position: absolute;
  top: 1.125rem; /* 1.8rem (old 18px) → 18 ÷ 16 = 1.125rem */
  left: 0;
  z-index: 100;
  transition: all 0.8s ease;
}

.headerScrolled {
  top: 0;
  background: rgba(235, 230, 226, 0.75);
  backdrop-filter: blur(1rem); /* 4.152rem old = 41.52px → 41.52 ÷ 16 = 2.595rem (approx 2.6rem) */
  -webkit-backdrop-filter: blur(2.6rem);
  box-shadow: 0 0.25rem 1.25rem rgba(18, 37, 63, 0.08); /* 4px & 20px converted */
  border-bottom: 1px solid rgba(235, 230, 226, 0.3);
  border: none;
}

.inner {
  max-width: clamp(20rem, 80vw, 94.2rem); /* 32rem→320px, 150.7rem→1507px → ÷16 = 94.2rem */
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1.25rem; /* 20px */
  padding: 0.625rem 1.25rem; /* 10px 20px */
  transition: all 0.3s ease;
}

.logo img {
  height: 5.625rem; /* 9rem old → 90px → 90 ÷ 16 = 5.625rem */
  width: 5.625rem;
}

.stickyClose {
  position: fixed;
  top: 2.5rem; /* 40px */
  right: 1.25rem; /* 20px */
  z-index: 50;
  background-color: var(--secondary);
  padding: 0.625rem; /* 10px */
  display: flex;
  justify-content: flex-end;
}

.nav_home {
  flex: 1;
  max-width: 59.9rem; /* 95.8rem→958px ÷16=59.9rem */
  margin: 0 1.25rem; /* 20px */
  padding: 0 0.625rem; /* 10px */
  border-radius: 4.375rem; /* 70px */
  backdrop-filter: blur(2.6rem); /* 41.52px */
  -webkit-backdrop-filter: blur(2.6rem);
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  position: relative;
  z-index: 10;
  background: linear-gradient(
    109.06deg,
    var(--nav-gradient-start) 31.6%,
    var(--nav-gradient-end) 113.76%
  );
  border-image-source: linear-gradient(
    90deg,
    var(--border-gradient-start) 10.67%,
    var(--border-gradient-end) 132.45%
  );
}

.nav {
  flex: 1;
  max-width: 59.9rem;
  margin: 0 1.25rem;
  padding: 0 0.625rem;
  border-radius: 4.375rem;
  background: transparent;
  border: none;
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  position: relative;
  z-index: 10;
}

.cta {
  flex-shrink: 0;
  font-family: "Outfit", sans-serif;
  font-weight: 600;
  font-size: 1.25rem; /* 20px */
  line-height: 120%;
  text-transform: uppercase;
  text-decoration: underline;
  text-decoration-thickness: 0.0625rem; /* 1px */
  color: var(--nav-link-active-color);
  background: none;
  border: none;
  cursor: pointer;
}

.navContainer {
  display: flex;
  height: 5rem; /* 80px */
  justify-content: space-around;
  align-items: center;
  width: 100%;
}

.linkList li,
.linkList ,
.socialList,
.socialList li{
  display: flex;
  list-style: none;
  align-items: center;
  justify-content: center;
  margin: 0;
  padding: 0;
  gap: 1.25rem; /* 20px */
}

.socialLink {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: var(--nav-link-active-color);
  padding: 0.625rem; /* 10px */
  border-radius: 50%;
}

.socialIcon {
  display: block;
  width: 0.844rem; /* 13.5px */
  height: 0.8125rem; /* 13px */
}

.link,
.activeLink {
  font-family: "Poppins", sans-serif;
  font-weight: 400;
  font-size: 1rem; /* 14px */
  line-height: 120%;
  text-transform: uppercase;
  text-decoration: none;
}

.activeLink {
  color: var(--nav-link-active-color);
  text-decoration: underline;
  text-decoration-thickness: 0.0625rem; /* 1px */
}

/* Mobile menu toggle button */
.mobileMenuToggle {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 12px; /* 1.2rem */
  color: inherit;
  z-index: 1001;
  transition: transform 0.3s ease;
  border-radius: 8px; /* 0.8rem */
  width: 48px; /* 4.8rem */
  height: 48px; /* 4.8rem */
  align-items: center;
  justify-content: center;
}

.mobileMenuToggle:hover {
  transform: scale(1.1);
  background-color: rgba(0, 0, 0, 0.05);
}

.mobileMenuToggle:active {
  transform: scale(0.95);
}

.mobileMenuToggle svg {
  width: 28px; /* 2.8rem */
  height: 28px; /* 2.8rem */
}

/* Mobile overlay */
.mobileOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.4s ease, visibility 0.4s ease;
}

.overlayOpen {
  opacity: 1;
  visibility: visible;
}

/* Mobile navigation */
.mobileNav {
  position: fixed;
  top: 0;
  right: -100%;
  height: 100vh;
  width: 320px; /* 32rem */
  max-width: 85vw;
  z-index: 1000;
  overflow-y: auto;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15); /* -0.4rem 0 2rem */
  transition: right 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobileNavOpen {
  right: 0;
}

.mobileNavContent {
  padding: 100px 30px 30px; /* 10rem 3rem 3rem */
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Mobile-specific navigation styles */
.mobileNav .navContainer {
  flex-direction: column;
  gap: 0;
  height: 100%;
  align-items: stretch;
}

.mobileNav .linkList {
  flex-direction: column;
  gap: 0;
  margin-bottom: 40px; /* 4rem */
}

.mobileNav .linkList li {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1); /* 0.1rem */
}

.mobileNav .linkList li:last-child {
  border-bottom: none;
}

.mobileNav .link,
.mobileNav .activeLink {
  display: block;
  padding: 16px 0; /* 1.6rem */
  font-size: 18px; /* 1.8rem */
  transition: padding-left 0.3s ease;
  border-left: 3px solid transparent; /* 0.3rem */
}

.mobileNav .link:hover,
.mobileNav .activeLink {
  padding-left: 12px; /* 1.2rem */
  border-left-color: currentColor;
}

.mobileNav .socialList {
  justify-content: center;
  gap: 20px; /* 2rem */
  margin-top: auto;
  padding-top: 30px; /* 3rem */
  border-top: 1px solid rgba(0, 0, 0, 0.1); /* 0.1rem */
}

.mobileNav .socialIcon {
  width: 28px; /* 2.8rem */
  height: 28px; /* 2.8rem */
  transition: transform 0.3s ease;
}

.mobileNav .socialLink:hover .socialIcon {
  transform: scale(1.2);
}

.mobileCta {
  margin-top: 30px; /* 3rem */
  text-align: center;
}


.whiteText {
  color: white !important;
}

/* Responsive adjustments for large-medium screens (1700px and below) */
@media (max-width: 1700px) {
  .inner {
    gap: 1.5rem; /* stays fine */
    padding: 1rem 1.5rem;
  }

  .logo img {
    height: 90px; /* was 9rem → now px */
    width: 90px;
  }

  .navContainer {
    height: 60px; /* was 6rem */
  }

  .linkList {
    gap: 10px; /* was 1rem */
  }

  .socialList {
    gap: 10px;
  }

  .link,
  .activeLink {
    font-size: 12px; /* was 1.2rem */
    padding: 6px 10px; /* was 0.6rem 1rem */
  }

  .cta {
    font-size: 16px; /* was 1.6rem */
  }

  .socialLink {
    padding: 8px; /* was 0.8rem */
  }

  .socialIcon {
    width: 11px; /* was 1.1rem */
    height: 11px;
  }
}

/* Medium screens (1200px and below) */
@media (max-width: 1200px) {
  .logo img {
    height: 70px; /* 7rem */
    width: 70px;
  }

  .nav {
    max-width: 550px; /* 55rem */
    margin: 0 10px; /* 1rem */
    padding: 0 8px; /* 0.8rem */
  }

  .navContainer {
    height: 45px; /* 4.5rem */
  }

  .link,
  .activeLink {
    font-size: 10px; /* 1rem */
    padding: 5px 8px; /* 0.5rem 0.8rem */
  }

  .socialIcon {
    width: 8px; /* 0.8rem */
    height: 8px;
  }

  .cta {
    font-size: 12px; /* 1.2rem */
  }
}

/* Smaller medium screens (1024px and below) */
@media (max-width: 1024px) {
  .inner {
    gap: 10px; /* 1rem */
  }

  .logo img {
    height: 65px; /* 6.5rem */
    width: 65px;
  }

  .navContainer {
    height: 40px; /* 4rem */
  }

  .linkList {
    gap: 8px; /* 0.8rem */
  }

  .socialList {
    gap: 8px; /* 0.8rem */
  }

  .link,
  .activeLink {
    font-size: 10px; /* 1rem */
    padding: 2px 6px; /* 0.2rem 0.6rem */
  }

  .cta {
    font-size: 11px; /* 1.1rem */
  }

  .socialLink {
    padding: 6px; /* 0.6rem */
  }

  .socialIcon {
    width: 8px; /* 0.8rem */
    height: 8px;
  }
}

/* Mobile screens (900px and below) */
@media (max-width: 900px) {
  .whiteText {
    color: black !important;
  }

  .inner {
    flex-direction: row;
    justify-content: space-between;
    min-height: 70px; /* 7rem */
    padding: 10px 20px; /* 1rem 2rem */
  }

  .logo {
    max-width: 120px; /* 12rem */
    height: 75px; /* 7.5rem */
  }

  .logo img,
  .logo svg,
  .logo * {
    max-width: 100%;
    max-height: 75px;
    width: auto;
    height: auto;
    object-fit: contain;
  }

  .mobileMenuToggle {
    display: flex;
  }

  .desktopNav,
  .desktopCta {
    display: none;
  }

  .nav {
    background-color: #fff;
  }
}

/* Tablet adjustments (481px - 900px) */
@media (max-width: 900px) and (min-width: 481px) {
  .logo {
    max-width: 140px; /* 14rem */
    height: 75px;
  }

  .logo img,
  .logo svg,
  .logo * {
    max-height: 75px;
  }

  .mobileMenuToggle {
    width: 55px; /* 5.5rem */
    height: 55px;
  }

  .mobileMenuToggle svg {
    width: 50px; /* 5rem */
    height: 50px;
  }
}

/* Small mobile adjustments (480px and below) */
@media (max-width: 480px) {
  .inner {
    padding: 8px 15px; /* 0.8rem 1.5rem */
    min-height: 60px; /* 6rem */
  }

  .logo {
    max-width: 100px; /* 10rem */
    height: 75px; /* 7.5rem */
  }

  .logo img,
  .logo svg,
  .logo * {
    max-height: 75px;
  }

  .mobileMenuToggle {
    width: 55px;
    height: 55px;
  }

  .mobileMenuToggle svg {
    width: 50px;
    height: 50px;
  }
}

/* Desktop styles - hide mobile elements */
@media (min-width: 900px) {
  .mobileMenuToggle,
  .mobileOverlay,
  .mobileNav {
    display: none;
  }
}
