import { useState } from "react";
const firstImg = "/images/A_2.webp";
const secondImg = "/images/heroImage.webp";
import CarouselController from "../../../../components/Carousel/components/CarouselController";
import Subtitle from "./../../../../components/Subtitle/index";

const carouselData = [
  {
    id: 1,
    subtitle: "Advatnages",
    title: "Investing opportunities",
    desc: "Our end-to-end service gives us a complete view of each project, enabling seamless, high-impact retail solutions that drive results for brands worldwide.",
    image: firstImg,
  },
  {
    id: 2,
    subtitle: "Advatnages",
    title: "Investing prospects",
    desc: "Our end-to-end service gives us a complete view of each project, enabling seamless, high-impact retail solutions that drive results for brands worldwide.",
    image: secondImg,
  },

  {
    id: 4,
    subtitle: "Advatnages",
    title: "Investing opportunities",
    desc: "Our end-to-end service gives us a complete view of each project, enabling seamless, high-impact retail solutions that drive results for brands worldwide.",
    image: firstImg,
  },
  {
    id: 3,
    subtitle: "Advatnages",
    title: "Investing prospects",
    desc: "Our end-to-end service gives us a complete view of each project, enabling seamless, high-impact retail solutions that drive results for brands worldwide.",
    image: secondImg,
  },
];

function Slides() {
  const [activeIndex, setActiveIndex] = useState(0);
  const activeSlide = carouselData[activeIndex];

  return (
    <section className="py-20 bg-[#EBE6E2] text-secondary">
      <div className="container container-smaller mx-auto  px-6 md:px-30">
        <div className="mt-15 flex flex-wrap justify-center md:justify-start gap-y-10 gap-x-24 w-full mb-20 ">
          <img
            src={activeSlide.image}
            alt={activeSlide.title}
            className="object-cover max-h-[724px]"
            style={{ aspectRatio: "559/724" }}
          />
          <div className="mx-14 md:m-0 text-center md:text-start md:max-w-[38%] flex flex-col justify-evenly items-center md:items-start gap-6 w-full">
            <Subtitle text={activeSlide.subtitle} className="hidden md:block" />
            <h2 className="text-6xl">{activeSlide.title}</h2>
            <p className="font-outfit font-normal text-2xl">
              Our end-to-end service gives us a complete view of each project,
              enabling seamless, high-impact retail solutions that drive results
              for brands worldwide.
            </p>
          </div>
        </div>
        <CarouselController
          activeIndex={activeIndex}
          itemsPerIndex={1}
          totalItems={carouselData.length}
          onIndexChange={setActiveIndex}
          carouselData={carouselData}
        />
      </div>
    </section>
  );
}

export default Slides;
