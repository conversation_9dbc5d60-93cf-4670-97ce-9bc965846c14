import { useState } from "react";
const firstImg = "/images/A_2.webp";
const secondImg = "/images/heroImage.webp";
import CarouselController from "../../../../components/Carousel/components/CarouselController";
import Subtitle from "./../../../../components/Subtitle/index";

const carouselData = [
  {
    id: 1,
    subtitle: "Advatnages",
    title: "Investing opportunities",
    desc: "Our end-to-end service gives us a complete view of each project, enabling seamless, high-impact retail solutions that drive results for brands worldwide.",
    image: firstImg,
  },
  {
    id: 2,
    subtitle: "Advatnages",
    title: "Investing prospects",
    desc: "Our end-to-end service gives us a complete view of each project, enabling seamless, high-impact retail solutions that drive results for brands worldwide.",
    image: secondImg,
  },

  {
    id: 4,
    subtitle: "Advatnages",
    title: "Investing opportunities",
    desc: "Our end-to-end service gives us a complete view of each project, enabling seamless, high-impact retail solutions that drive results for brands worldwide.",
    image: firstImg,
  },
  {
    id: 3,
    subtitle: "Advatnages",
    title: "Investing prospects",
    desc: "Our end-to-end service gives us a complete view of each project, enabling seamless, high-impact retail solutions that drive results for brands worldwide.",
    image: secondImg,
  },
];

function Slides() {
  const [activeIndex, setActiveIndex] = useState(0);
  const activeSlide = carouselData[activeIndex];

  return (
    <section className="py-20 bg-[#EBE6E2] text-secondary">
      <div className="container container-smaller mx-auto px-4 sm:px-6 lg:px-8 xl:px-12">
        <div className="mt-8 lg:mt-12 xl:mt-15 flex flex-col lg:flex-row justify-center lg:justify-start gap-8 lg:gap-12 xl:gap-16 w-full mb-12 lg:mb-16 xl:mb-20">
          <div className="flex justify-center lg:justify-start">
            <img
              src={activeSlide.image}
              alt={activeSlide.title}
              className="object-cover w-full max-w-sm sm:max-w-md lg:max-w-lg xl:max-w-xl max-h-[400px] sm:max-h-[500px] lg:max-h-[600px] xl:max-h-[724px]"
              style={{ aspectRatio: "559/724" }}
            />
          </div>
          <div className="px-4 sm:px-8 lg:px-0 text-center lg:text-start lg:max-w-[45%] xl:max-w-[38%] flex flex-col justify-center lg:justify-evenly items-center lg:items-start gap-4 sm:gap-6 w-full">
            <Subtitle text={activeSlide.subtitle} className="hidden lg:block" />
            <h2 className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl leading-tight">{activeSlide.title}</h2>
            <p className="font-outfit font-normal text-base sm:text-lg lg:text-xl xl:text-2xl">
              Our end-to-end service gives us a complete view of each project,
              enabling seamless, high-impact retail solutions that drive results
              for brands worldwide.
            </p>
          </div>
        </div>
        <CarouselController
          activeIndex={activeIndex}
          itemsPerIndex={1}
          totalItems={carouselData.length}
          onIndexChange={setActiveIndex}
          carouselData={carouselData}
        />
      </div>
    </section>
  );
}

export default Slides;
