import { FiStar, FiCheck } from "react-icons/fi";
import { FiCheckCircle } from "react-icons/fi";


function ResaleAmenities({ property }) {
  if (!property?.amenities || property.amenities.length === 0) return null;

  return (
    <section className="mb-10 h-full">
      <div className="bg-white rounded-2xl shadow-xl p-8 h-full flex flex-col">
        {/* Title */}
        <div className="flex items-center gap-3 mb-8">
          <FiStar className="text-3xl md:text-4xl text-primary" />
          <h3 className="text-3xl md:text-4xl font-extrabold text-primary">
            Amenities
          </h3>
        </div>

        {/* Amenities Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 flex-1 overflow-auto">
          {property.amenities.map((amenity, index) => (
            <div
              key={index}
              className="flex items-center gap-4 bg-gray-50 rounded-xl px-5 py-4 shadow-sm hover:shadow-md hover:bg-gray-100 transition-all"
            >
          <FiCheckCircle className="text-2xl text-primary" />
              <span className="text-gray-700 font-semibold text-lg md:text-lg">
                {amenity}
              </span>
            </div>
          ))}
        </div>

        {/* Amenities count */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <p className="text-center text-gray-700 font-medium text-lg md:text-xl">
            <span className="text-primary font-extrabold text-xl md:text-2xl">
              {property.amenities.length}
            </span>{" "}
            premium amenities included
          </p>
        </div>
      </div>
    </section>
  );
}

export default ResaleAmenities;
