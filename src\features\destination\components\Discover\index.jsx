import Carousel from "../../../../components/Carousel";
import Subtitle from "../../../../components/Subtitle/index";
const firstImg = "/images/D_1.webp";
const secondImg = "/images/D_2.webp";
const thirdImg = "/images/D_3.webp";

const carouselData = [
  {
    id: 1,
    title: "Bluewaters Island",
    price: "from 1.8 AED",
    image: thirdImg,
  },
  {
    id: 2,
    title: "Jumeirah Village Circle",
    price: "from 1.8 AED",
    image: secondImg,
  },
  {
    id: 3,
    title: "City Walk",
    price: "from 1.8 AED",
    image: firstImg,
  },

  {
    id: 4,
    title: "Jumeirah Village Circle",
    price: "from 1.8 AED",
    image: secondImg,
  },
  {
    id: 5,
    title: "Bluewaters Island",
    price: "from 1.8 AED",
    image: thirdImg,
  },
  {
    id: 6,
    title: "City Walk",
    price: "from 1.8 AED",
    image: firstImg,
  },

  {
    id: 7,
    title: "Bluewaters Island",
    price: "from 1.8 AED",
    image: thirdImg,
  },
  {
    id: 8,
    title: "City Walk",
    price: "from 1.8 AED",
    image: firstImg,
  },
  {
    id: 9,
    title: "Jumeirah Village Circle",
    price: "from 1.8 AED",
    image: secondImg,
  },

  {
    id: 10,
    title: "City Walk",
    price: "from 1.8 AED",
    image: firstImg,
  },
];

function Discover() {
  return (
    <section className="py-20 bg-secondary text-white">
      <div className="container mx-auto">
        <Subtitle text="Top Areas in Dubai" />
        <div className="grid md:grid-cols-12 gap-5">
          <h2 className="md:col-span-8 text-5xl">
            Discover Dubai's{" "}
            <span className="text-primary italic font-semibold">
              Top Areas !
            </span>
          </h2>
        </div>

        <div className="mt-10">
          <Carousel carouselData={carouselData} isDark={true} />
        </div>
      </div>
    </section>
  );
}

export default Discover;
