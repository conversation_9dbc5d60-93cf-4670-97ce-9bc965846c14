// src/store/customerReviewsStore.js
import { create } from "zustand";
import axios from "axios";

const useCustomerReviewsStore = create((set) => ({
  customerReviews: [],
  paginationInfo: {
    totalItems: 0,
    totalPages: 0,
    pageSize: 10,
    pageNumber: 1,
  },
  loading: false,
  error: null,

  // Fetch reviews from API
  fetchCustomerReviews: async (pageNumber = 1, pageSize = 10) => {
    set({ loading: true, error: null });
    try {
      const response = await axios.get(
        `${import.meta.env.VITE_API_BASE_URL}/api/customer-reviews?pageSize=${pageSize}&pageNumber=${pageNumber}`
      );

      set({
        customerReviews: response.data.customerReviews || [],
        paginationInfo: response.data.paginationInfo || {},
        loading: false,
      });
    } catch (error) {
      console.error("Error fetching customer reviews:", error);
      set({ error: error.message || "Failed to load customer reviews", loading: false });
    }
  },
}));

export default useCustomerReviewsStore;
